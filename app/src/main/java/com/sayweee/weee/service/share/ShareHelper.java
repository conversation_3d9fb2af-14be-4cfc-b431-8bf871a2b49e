package com.sayweee.weee.service.share;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.Application;
import android.app.Dialog;
import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.ActivityInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffXfermode;
import android.graphics.Rect;
import android.graphics.RectF;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.Build;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.core.content.FileProvider;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.target.CustomTarget;
import com.bumptech.glide.request.transition.Transition;
import com.facebook.CallbackManager;
import com.facebook.FacebookCallback;
import com.facebook.FacebookException;
import com.facebook.share.Sharer;
import com.facebook.share.model.ShareLinkContent;
import com.facebook.share.model.SharePhoto;
import com.facebook.share.model.SharePhotoContent;
import com.facebook.share.widget.ShareDialog;
import com.kakao.sdk.common.util.KakaoCustomTabsClient;
import com.kakao.sdk.share.ShareClient;
import com.kakao.sdk.share.WebSharerClient;
import com.kakao.sdk.share.model.SharingResult;
import com.sayweee.logger.Logger;
import com.sayweee.weee.R;
import com.sayweee.weee.global.config.AppConfig;
import com.sayweee.weee.global.config.AppFilter;
import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.global.config.DevConfig;
import com.sayweee.weee.global.manager.LanguageManager;
import com.sayweee.weee.module.cart.bean.ShareBean;
import com.sayweee.weee.module.web.ImageWebActivity;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackModel;
import com.sayweee.weee.service.rx.SimpleCompletableObserver;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.DecimalTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.FileUtils;
import com.sayweee.weee.utils.ScreenshotUtil;
import com.sayweee.weee.utils.listener.OnSafeClickListener;
import com.sayweee.wrapper.base.view.ViewHelper;
import com.sayweee.wrapper.base.view.WrapperDialog;
import com.sayweee.wrapper.helper.lifecycle.LifecycleProvider;
import com.sayweee.wrapper.helper.loading.LoadingProvider;
import com.sayweee.widget.toaster.Toaster;
import com.tencent.mm.opensdk.modelmsg.SendMessageToWX;
import com.tencent.mm.opensdk.modelmsg.WXImageObject;
import com.tencent.mm.opensdk.modelmsg.WXMediaMessage;
import com.tencent.mm.opensdk.modelmsg.WXWebpageObject;
import com.tencent.mm.opensdk.openapi.IWXAPI;
import com.tencent.mm.opensdk.openapi.WXAPIFactory;
import com.tiktok.open.sdk.core.appcheck.TikTokAppCheckUtil;

import java.io.File;
import java.io.FileOutputStream;
import java.io.FilenameFilter;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import io.reactivex.Completable;
import io.reactivex.schedulers.Schedulers;
import kotlin.Unit;
import kotlin.jvm.functions.Function2;


/**
 * Author:  winds
 * Email:   <EMAIL>
 * Date:    2020/12/19.
 * Desc:
 */
public class ShareHelper {

    private static final String SHARE_IMAGES_DIR = "share_images";
    private static final String SHARE_IMAGES_PREFIX = "share_";
    private static final String SHARE_IMAGES_EXT = ".png";

    public interface ShareCallback {
        void onResult(String channel, boolean success, String data);
    }

    public interface FacebookShareCallback<T> {
        void onSuccessful(T t);
    }

    private interface CompletionHandler<P> {
        void onComplete(P p);
    }

    private static CallbackManager callbackManager;
    private static boolean sSharedImageCleanup = false;

    public static CallbackManager getCallBackManager() {
        return callbackManager;
    }

    /**
     * 分享到Facebook
     *
     * @param url 链接
     */
    public static void shareFacebook(String url, final FacebookShareCallback<Sharer.Result> callback) {
        ShareLinkContent shareLinkContent =
                new ShareLinkContent.Builder().setContentUrl(Uri.parse(url))
                        .build();
        ShareDialog shareDialog = new ShareDialog(getActivity());
        if (callbackManager == null) {
            callbackManager = CallbackManager.Factory.create();
        }
        shareDialog.registerCallback(callbackManager, new FacebookCallback<Sharer.Result>() {
            @Override
            public void onSuccess(Sharer.Result result) {
                callback.onSuccessful(result);
                onShareResult(Constants.ShareChannel.FACEBOOK, 1);
            }

            @Override
            public void onCancel() {
            }

            @Override
            public void onError(FacebookException error) {
                Log.e("Share", error.getMessage());
            }
        });
        shareDialog.show(shareLinkContent);
    }

    /**
     * 分享到微信
     *
     * @param imageUrl    图片网址
     * @param title       标题
     * @param description 描述
     * @param url         链接
     */
    public static void shareWechat(String imageUrl, final String title, final String description, final String url) {
        downloadThumb(imageUrl, bitmap ->
                shareWechat(SendMessageToWX.Req.WXSceneSession, bitmap, title, description, url));
    }

    /**
     * 分享到微信朋友圈
     *
     * @param imageUrl    图片网址
     * @param title       标题
     * @param description 描述
     * @param url         链接
     */
    public static void shareWechatMoment(String imageUrl, String title, String description, String url) {
        downloadThumb(imageUrl, bitmap ->
                shareWechat(SendMessageToWX.Req.WXSceneTimeline, bitmap, title, description, url));
    }

    /**
     * 分享到短信
     *
     * @param title       标题
     * @param description 描述
     * @param url         链接
     */
    public static void shareSms(String title, String description, String url) {
        Uri uri = Uri.parse("smsto:" + "");
        Intent intent = new Intent(Intent.ACTION_SENDTO, uri);
        intent.putExtra("sms_body", connectShareContent(title, description, url));
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        boolean start = startActivity(intent);
        if (start) {
            onShareResult(Constants.ShareChannel.SMS, 1);
        }
    }

    /**
     * 拷贝内容到剪切板
     *
     * @param text 需要被拷贝的内容
     */
    public static void copyLink(String text) {
        Context context = LifecycleProvider.get().getTopActivity();
        copyLink(text, context != null ? context.getString(R.string.s_link_copied) : null);
    }

    public static void copyLink(String text, String toast) {
        try {
            Application application = LifecycleProvider.get().getApplication();
            if (application != null) {
                ClipboardManager clip = (ClipboardManager) application.getSystemService(Context.CLIPBOARD_SERVICE);
                clip.setPrimaryClip(ClipData.newPlainText(null, text));
                Toaster.showToast(toast);
            }
        } catch (Exception e) {/**/}
    }

    /**
     * 系统分享
     *
     * @param title       标题
     * @param description 描述
     */
    @SuppressLint("UnspecifiedImmutableFlag")
    @SuppressWarnings("squid:S5322")
    public static void shareSystem(String title, String description, String shareLanguage) {
        Intent intent = new Intent(Intent.ACTION_SEND);
        intent.putExtra(Intent.EXTRA_SUBJECT, title);
        intent.putExtra(Intent.EXTRA_TEXT, description);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        intent.setType("text/plain");
        shareSystem(intent, shareLanguage);
    }

    @Nullable
    private static Intent createShareImageIntent(Bitmap bitmap) {
        Context context = LifecycleProvider.get().getApplication();
        if (context != null) {
            File dir = new File(context.getExternalCacheDir(), SHARE_IMAGES_DIR);
            File imageFile = new File(dir, SHARE_IMAGES_PREFIX + System.currentTimeMillis() + SHARE_IMAGES_EXT);
            boolean success = true;
            try {
                if (!dir.exists()) {
                    success = dir.mkdirs();
                }
                success = success && imageFile.createNewFile();
            } catch (Exception e) {
                return null;
            }
            if (success) {
                try (OutputStream os = new FileOutputStream(imageFile)) {
                    bitmap.compress(Bitmap.CompressFormat.PNG, 100, os);
                    os.flush();
                } catch (Exception e) {
                    return null;
                }
            } else {
                return null;
            }

            Uri imageUri = FileProvider.getUriForFile(context, DevConfig.APPLICATION_ID, imageFile);
            Intent sendIntent = new Intent();
            sendIntent.setAction(Intent.ACTION_SEND);
            sendIntent.setType("image/png");
            sendIntent.setClipData(ClipData.newRawUri(null, imageUri));
            sendIntent.putExtra(Intent.EXTRA_STREAM, imageUri);
            sendIntent.setFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
            return sendIntent;
        }
        return null;
    }

    private static boolean shareSystemImage(Bitmap bitmap) {
        Intent intent = createShareImageIntent(bitmap);
        if (intent != null) {
            shareSystem(intent, null);
            return true;
        }
        return false;
    }

    @SuppressLint("UnspecifiedImmutableFlag")
    @SuppressWarnings("squid:S5322")
    private static void shareSystem(Intent intent, String shareLanguage) {
        Intent chooser = null;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP_MR1) {
            BroadcastReceiver broadcastReceiver = new BroadcastReceiver() {
                @Override
                public void onReceive(Context context, Intent intent) {
                    if (intent != null) {
                        ComponentName component = intent.getParcelableExtra(Intent.EXTRA_CHOSEN_COMPONENT);
                        if (component != null) {
                            String packageName = component.getPackageName();
                            String className = component.getClassName();
                            StringBuilder builder = new StringBuilder();
                            if (packageName != null) {
                                builder.append(packageName).append("/");
                            }
                            if (className != null) {
                                builder.append(className);
                            }
                            if (builder.length() > 0) {
                                trackShareSystem(builder.toString(), shareLanguage);
                            }
                        }
                    }
                    try {
                        context.unregisterReceiver(this);
                    } catch (Exception ignored) {
                    }
                }
            };

            try {
                Application application = LifecycleProvider.get().getApplication();
                final String shareAction = "com.sayweee.share.SHARE_ACTION";
                Intent receiver = new Intent(shareAction);
                receiver.putExtra("test", "test");
                PendingIntent pendingIntent;
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    receiver.setPackage(DevConfig.APPLICATION_ID);
                    pendingIntent = PendingIntent.getBroadcast(application, 0, receiver, PendingIntent.FLAG_MUTABLE | PendingIntent.FLAG_UPDATE_CURRENT);
                } else {
                    pendingIntent = PendingIntent.getBroadcast(application, 0, receiver, PendingIntent.FLAG_UPDATE_CURRENT);
                }
                chooser = Intent.createChooser(intent, "share", pendingIntent.getIntentSender());
                ContextCompat.registerReceiver(application, broadcastReceiver, new IntentFilter(shareAction),
                        ContextCompat.RECEIVER_NOT_EXPORTED);
            } catch (Exception ignored) {
                Log.e("resion", "shareSystem", ignored);
            }
        } else {
            chooser = Intent.createChooser(intent, "share");
        }
        startActivity(chooser);
    }

    private static void shareFacebookFeed(String title, String link) {
        shareFacebookSdk(link);
    }

    private static void shareFacebookSdk(String link) {
        ShareHelper.shareFacebook(link, new ShareHelper.FacebookShareCallback<Sharer.Result>() {
            @Override
            public void onSuccessful(Sharer.Result result) {
                Logger.toJson(result);
            }
        });
    }

    private static void shareFacebookGroup(String link, String title) {
        Intent shareIntent = new Intent(android.content.Intent.ACTION_SEND);
        shareIntent.setType("text/plain");
        shareIntent.putExtra(android.content.Intent.EXTRA_TEXT, title);
        shareIntent.putExtra(android.content.Intent.EXTRA_SUBJECT, link);

        PackageManager pm = getActivity().getPackageManager();
        List<ResolveInfo> activityList = pm.queryIntentActivities(shareIntent, 0);
        for (final ResolveInfo app : activityList) {
            if (EmptyUtils.isEmpty(app.activityInfo) || EmptyUtils.isEmpty(app.activityInfo.name) || EmptyUtils.isEmpty(app.activityInfo.packageName)) {
                continue;
            }
            if ((app.activityInfo.packageName).contains("com.facebook.katana") && (app.activityInfo.name).contains("Groups")) {
                final ActivityInfo activity = app.activityInfo;
                final ComponentName name = new ComponentName(
                        activity.applicationInfo.packageName,
                        activity.name);
                shareIntent.addCategory(Intent.CATEGORY_LAUNCHER);
                shareIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                shareIntent.setComponent(name);
                startActivity(Intent.createChooser(shareIntent, "share"));
                return;
            }
        }
    }

    private static boolean shareFacebookFeedIntent(String title, String link) {
        if (!isFacebookInstall(getActivity())) {
            return false;
        }
        Intent shareIntent = new Intent(android.content.Intent.ACTION_SEND);
        shareIntent.setType("text/plain");
        shareIntent.putExtra(android.content.Intent.EXTRA_TEXT, title);
        shareIntent.putExtra(android.content.Intent.EXTRA_SUBJECT, link);

        PackageManager pm = getActivity().getPackageManager();
        List<ResolveInfo> activityList = pm.queryIntentActivities(shareIntent, 0);
        for (final ResolveInfo app : activityList) {
            if (EmptyUtils.isEmpty(app.activityInfo) || EmptyUtils.isEmpty(app.activityInfo.name) || EmptyUtils.isEmpty(app.activityInfo.packageName)) {
                continue;
            }
            if ((app.activityInfo.packageName).contains("com.facebook.katana") && !(app.activityInfo.name).contains("Groups")) {
                final ActivityInfo activity = app.activityInfo;
                final ComponentName name = new ComponentName(
                        activity.applicationInfo.packageName,
                        activity.name);
                shareIntent.addCategory(Intent.CATEGORY_LAUNCHER);
                shareIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                shareIntent.setComponent(name);
                startActivity(Intent.createChooser(shareIntent, "share"));
                return true;
            }
        }
        return false;
    }

    private static boolean haveFacebookGroup() {
        if (!isFacebookInstall(getActivity())) {
            return false;
        }
        Intent shareIntent = new Intent(android.content.Intent.ACTION_SEND);
        shareIntent.setType("text/plain");
        PackageManager pm = getActivity().getPackageManager();
        List<ResolveInfo> activityList = pm.queryIntentActivities(shareIntent, 0);
        for (final ResolveInfo app : activityList) {
            if (EmptyUtils.isEmpty(app.activityInfo) || EmptyUtils.isEmpty(app.activityInfo.name) || EmptyUtils.isEmpty(app.activityInfo.packageName)) {
                continue;
            }
            if ((app.activityInfo.packageName).contains("com.facebook.katana") && (app.activityInfo.name).contains("Groups")) {
                return true;
            }
        }
        return false;
    }

    public static void shareFile(Uri uri) {
        Intent intent = new Intent(Intent.ACTION_SEND);
        intent.putExtra(Intent.EXTRA_STREAM, uri);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        intent.setType("text/plain");
        startActivity(Intent.createChooser(intent, ""));
    }

    /**
     * 分享到WhatsApp
     *
     * @param url 链接
     */
    public static void shareWhatsApp(String title, String desc, String url) {
        shareBySystem(connectShareContent(title, desc, url), "com.whatsapp", Constants.ShareChannel.WHATSAPP);
    }

    public static void shareMessenger(String title, String desc, String url) {
        shareBySystem(connectShareContent(title, desc, url), "com.facebook.orca", Constants.ShareChannel.MESSENGER);
    }

    public static void shareLine(String title, String desc, String url) {
        shareBySystem(connectShareContent(title, desc, url), "jp.naver.line.android", Constants.ShareChannel.LINE);
    }

    public static void shareInstagram(String title, String desc, String url) {
        shareBySystem(connectShareContent(title, desc, url), "com.instagram.android", Constants.ShareChannel.INSTAGRAM);
    }

    public static void shareBySystem(String url, String packageName, String channel) {
        if (url != null && packageName != null) {
            Intent sendIntent = new Intent();
            sendIntent.setAction(Intent.ACTION_SEND);
            sendIntent.putExtra(Intent.EXTRA_TEXT, url);
            sendIntent.setType("text/plain");
            sendIntent.setPackage(packageName);
            boolean start = startActivity(sendIntent);
            if (start) {
                onShareResult(channel, 1);
            }
        }
    }

    private static void shareWechat(int scene, Bitmap thumbImage, String title, String description, String url) {
        IWXAPI api = WXAPIFactory.createWXAPI(LifecycleProvider.get().getApplication(), AppConfig.KEY_WX_ID, false);
        WXWebpageObject webpage = new WXWebpageObject();
        webpage.webpageUrl = url;

        //用 WXWebpageObject 对象初始化一个 WXMediaMessage 对象
        WXMediaMessage msg = new WXMediaMessage(webpage);
        msg.title = title;
        msg.description = description;
        Bitmap bitmap = getRoundCornerImage(thumbImage, 0);
        if (bitmap != null) {
            msg.setThumbImage(bitmap);
        }
        //构造一个Req
        SendMessageToWX.Req req = new SendMessageToWX.Req();
        req.transaction = buildTransaction("webpage");
        req.message = msg;
        req.scene = scene; //SendMessageToWX.Req.WXSceneSession;
        //调用api接口，发送数据到微信
        api.sendReq(req);
    }

    private static String connectShareContent(String title, String description, String url) {
        StringBuilder builder = new StringBuilder();
        if (!EmptyUtils.isEmpty(title)) {
            builder.append(title);
        }
        if (!TextUtils.isEmpty(description)) {
            if (builder.length() > 0) {
                builder.append("\n");
            }
            builder.append(description);
        }
        if (!TextUtils.isEmpty(url)) {
            if (builder.length() > 0) {
                builder.append("\n");
            }
            builder.append(url);
        }
        return builder.toString();
    }

    private static void downloadThumb(String imageUrl, final CompletionHandler<Bitmap> completionHandler) {
        Activity activity = getActivity();
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        Glide.with(activity).asBitmap().load(imageUrl).into(new CustomTarget<Bitmap>(100, 100) {

            @Override
            public void onLoadCleared(@Nullable Drawable placeholder) {
                // no op
            }

            @Override
            public void onResourceReady(@NonNull Bitmap resource, @Nullable Transition<? super Bitmap> transition) {
                completionHandler.onComplete(resource);
            }

            @Override
            public void onLoadFailed(@Nullable Drawable errorDrawable) {
                super.onLoadFailed(errorDrawable);
                completionHandler.onComplete(null);
            }
        });
    }

    private static String buildTransaction(final String type) {
        return (type == null) ? String.valueOf(System.currentTimeMillis()) : type + System.currentTimeMillis();
    }

    private static boolean startActivity(Intent intent) {
        Activity activity = LifecycleProvider.get().getTopActivity();
        if (activity != null) {
            try {
                activity.startActivity(intent);
                return true;
            } catch (Exception e) {/**/}
        }
        return false;
    }

    private static Activity getActivity() {
        return LifecycleProvider.get().getTopActivity();
    }

    @Deprecated
    private static void saveBitmap(final Bitmap bitmap, final String fileName, final CompletionHandler<File> completionHandler) {
        try {
            // 创建文件流，指向该路径，文件名叫做fileName
            String fileDirectory = CommonTools.getStoragePath(getActivity(), AppConfig.PATH_TEMP_NAME) + "/pics";
            File file = new File(fileDirectory, fileName);
            // file其实是图片，它的父级File是文件夹，判断一下文件夹是否存在，如果不存在，创建文件夹
            File fileParent = file.getParentFile();
            if (!fileParent.exists()) { // 文件夹不存在
                fileParent.mkdirs();// 创建文件夹
            }
            // 将图片保存到本地
            Bitmap.CompressFormat compressFormat = Bitmap.CompressFormat.WEBP;
            if (fileName.toLowerCase().endsWith(".jpeg") || fileName.toLowerCase().endsWith(".jpg")) {
                compressFormat = Bitmap.CompressFormat.JPEG;
            } else if (fileName.toLowerCase().endsWith(".png")) {
                compressFormat = Bitmap.CompressFormat.PNG;
            }
            bitmap.compress(compressFormat, 100, new FileOutputStream(file));
            completionHandler.onComplete(file);
        } catch (Exception e) {
            // e.printStackTrace();
        }
    }

    private static String shareResultTip;

    private static void fillShareResultTip(String type, String successTip) {
        if (type != null) {
            switch (type) {
                case Constants.ShareChannel.FACEBOOK:
                case Constants.ShareChannel.MESSENGER:
                case Constants.ShareChannel.WECHAT:
                case Constants.ShareChannel.WECHAT_MOMENT:
                case Constants.ShareChannel.KAKAO:
                    shareResultTip = successTip;
                    break;
                default:
                    shareResultTip = null;
                    break;
            }
        }
    }

    public static boolean execShareImage(String type, Bitmap image, String successTip, ShareCallback callback) {
        fillShareResultTip(type, successTip);
        boolean processed = false;
        boolean resultDispatched = false;
        switch (EmptyUtils.orEmpty(type)) {
            case Constants.ShareChannel.FACEBOOK:
                processed = shareFacebookImage(image, callback);
                resultDispatched = true;
                break;
            case Constants.ShareChannel.MESSENGER:
                processed = shareMessengerImage(image);
                break;
            case Constants.ShareChannel.WECHAT:
                processed = ShareHelper.shareWechatImage(image);
                break;
            case Constants.ShareChannel.WECHAT_MOMENT:
                processed = ShareHelper.shareWechatImageMoment(image);
                break;
            case Constants.ShareChannel.WHATSAPP:
                processed = ShareHelper.shareWhatsAppImage(image);
                break;
            case Constants.ShareChannel.KAKAO:
                processed = ShareHelper.shareKakaoImage(image);
                break;
            case Constants.ShareChannel.INSTAGRAM:
                processed = ShareHelper.shareInstagramImage(image);
                break;
            case Constants.ShareChannel.IMAGE:
                ScreenshotUtil.saveImage(image);
                processed = true;
                break;
            case Constants.ShareChannel.MORE:
                processed = ShareHelper.shareSystemImage(image);
                break;
            default:
                break;
        }
        if (processed && !resultDispatched) {
            dispatchShareResult(callback, type, true, null);
        }
        return processed;
    }

    public static void execShare(String type, String image, String title, String description, String link, String viewLink, String shareLanguage, String successTip, ShareCallback callback) {
        fillShareResultTip(type, successTip);
        if (type != null) {
            if (link != null && !link.isEmpty()) {
                String utm = "utm_source=";
                if (link.contains("?")) {
                    if (!link.contains(utm)) {
                        link = link + "&" + utm + type;
                    }
                } else {
                    link = link + "?" + utm + type;
                }
            }

            boolean resultDispatched = false;
            switch (type) {
                case Constants.ShareChannel.FACEBOOK:
                    shareFacebook(title, link, callback);
                    resultDispatched = true;
                    break;
                case Constants.ShareChannel.MESSENGER:
                    shareMessenger(title, description, link);
                    break;
                case Constants.ShareChannel.COPY_LINK:
                    ShareHelper.copyLink(link + " ");
                    break;
                case Constants.ShareChannel.SMS:
                    ShareHelper.shareSms(title, description, link);
                    break;
                case Constants.ShareChannel.WECHAT:
                    ShareHelper.shareWechat(image, title, description, link);
                    break;
                case Constants.ShareChannel.WECHAT_MOMENT:
                    ShareHelper.shareWechatMoment(image, title, description, link);
                    break;
                case Constants.ShareChannel.IMAGE:
                    toImagePage(viewLink);
                    break;
                case Constants.ShareChannel.EMAIL:
                    ShareHelper.shareEmail(title, description, link);
                    break;
                case Constants.ShareChannel.MORE:
                    ShareHelper.shareSystem(title, link, shareLanguage);
                    break;
                case Constants.ShareChannel.WHATSAPP:
                    ShareHelper.shareWhatsApp(title, description, link);
                    break;
                case Constants.ShareChannel.KAKAO:
                    ShareHelper.shareKakao(getActivity(), link);
                    break;
                case Constants.ShareChannel.LINE:
                    ShareHelper.shareLine(title, description, link);
                    break;
                case Constants.ShareChannel.INSTAGRAM:
                    ShareHelper.shareInstagram(title, description, link);
                    break;
            }

            if (!resultDispatched) {
                dispatchShareResult(callback, type, true, null);
            }
        }
    }

    private static void shareFacebook(String title, String link, ShareCallback callback) {
        if (haveFacebookGroup()) {
            showFaceBookDialog(title, link, callback);
        } else {
            shareFacebookFeed(title, link);
            dispatchShareResult(callback, Constants.ShareChannel.FACEBOOK, true, null);
        }
    }

    private static void showFaceBookDialog(String title, String link, ShareCallback callback) {
        new WrapperDialog(getActivity(), R.style.BottomDialogTheme) {
            @Override
            public void help(ViewHelper helper) {
                helper.setOnClickListener(new OnSafeClickListener() {
                    @Override
                    public void onClickSafely(View v) {
                        dismiss();
                        if (v.getId() == R.id.tv_facebook_feed) {
                            shareFacebookFeed(title, link);
                        } else if (v.getId() == R.id.tv_facebook_group) {
                            shareFacebookGroup(title, link);
                        }
                        dispatchShareResult(callback, Constants.ShareChannel.FACEBOOK, true, null);
                    }
                }, R.id.tv_facebook_feed, R.id.tv_facebook_group, R.id.tv_cancel);
            }

            @Override
            protected int getLayoutRes() {
                return R.layout.dialog_facebook_popup;
            }

            @Override
            protected void setDialogParams(Dialog dialog) {
                setDialogParams(dialog, WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.WRAP_CONTENT, Gravity.BOTTOM);
            }
        }.show();
    }

    private static void shareEmail(String title, String description, String link) {
        Intent intent = new Intent(Intent.ACTION_SENDTO);
        intent.putExtra(android.content.Intent.EXTRA_SUBJECT, title);
        intent.putExtra(android.content.Intent.EXTRA_TEXT, description + " " + link);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        boolean start = startActivity(Intent.createChooser(intent, ""));
        if (start) {
            onShareResult(Constants.ShareChannel.EMAIL, 1);
        }
    }

    private static void toImagePage(String link) {
        Activity activity = LifecycleProvider.get().getTopActivity();
        if (activity != null && link != null) {
            activity.startActivity(ImageWebActivity.getIntent(activity, link));
            activity.overridePendingTransition(0, 0);
        }
    }

    public static boolean isAppInstalled(Context context, String pkgName) {
        if (!TextUtils.isEmpty(pkgName)) {
            try {
                final PackageManager packageManager = context.getPackageManager();
                // 获取所有已安装程序的包信息
                List<PackageInfo> info = packageManager.getInstalledPackages(0);
                for (int i = 0; i < info.size(); i++) {
                    if (pkgName.equals(info.get(i).packageName)) {
                        return true;
                    }
                }
            } catch (Exception ignored) {
            }
        }
        return false;
    }

    public static boolean isWxInstall(Context context) {
        return isAppInstalled(context, "com.tencent.mm");
    }

    public static boolean isMessengerInstall(Context context) {
        return isAppInstalled(context, "com.facebook.orca");
    }

    public static boolean isWhatsAppInstall(Context context) {
        return isAppInstalled(context, "com.whatsapp");
    }

    public static boolean isKakaoInstall(Context context) {
        return isAppInstalled(context, "com.kakao.talk");
    }

    public static boolean isLineInstall(Context context) {
        return isAppInstalled(context, "jp.naver.line.android");
    }

    public static boolean isTiktokInstall(Context context) {
        return TikTokAppCheckUtil.INSTANCE.isTikTokAppInstalled(context);
    }

    public static boolean isInstagramInstall(Context context) {
        return isAppInstalled(context, "com.instagram.android");
    }

    public static boolean isFacebookInstall(Context context) {
        return isAppInstalled(context, "com.facebook.katana");
    }

    public static List<String> getSupportChannels() {
        ArrayList<String> supportLists = CollectionUtils.arrayListOf(
                Constants.ShareChannel.FACEBOOK,
                Constants.ShareChannel.EMAIL,
                Constants.ShareChannel.COPY_LINK,
                Constants.ShareChannel.SMS,
                Constants.ShareChannel.MORE
        );
        if (ShareHelper.isMessengerInstall(LifecycleProvider.get().getApplication())) {
            supportLists.add(Constants.ShareChannel.MESSENGER);
        }
        if (ShareHelper.isWxInstall(LifecycleProvider.get().getApplication())) {
            if (!AppFilter.ShareConfig.isNotSupport(Constants.ShareChannel.WECHAT)) {
                supportLists.add(Constants.ShareChannel.WECHAT);
            }
            if (!AppFilter.ShareConfig.isNotSupport(Constants.ShareChannel.WECHAT_MOMENT)) {
                supportLists.add(Constants.ShareChannel.WECHAT_MOMENT);
            }
        }
        if (ShareHelper.isWhatsAppInstall(LifecycleProvider.get().getApplication())) {
            supportLists.add(Constants.ShareChannel.WHATSAPP);
        }
        if (ShareHelper.isKakaoInstall(LifecycleProvider.get().getApplication())) {
            if (!AppFilter.ShareConfig.isNotSupport(Constants.ShareChannel.KAKAO)) {
                supportLists.add(Constants.ShareChannel.KAKAO);
            }
        }
        if (ShareHelper.isLineInstall(LifecycleProvider.get().getApplication())) {
            if (!AppFilter.ShareConfig.isNotSupport(Constants.ShareChannel.LINE)) {
                supportLists.add(Constants.ShareChannel.LINE);
            }
        }
        if (ShareHelper.isInstagramInstall(LifecycleProvider.get().getApplication())) {
            supportLists.add(Constants.ShareChannel.INSTAGRAM);
        }
        return supportLists;
    }

    /**
     * 画成圆角图片
     */
    private static Bitmap getRoundCornerImage(Bitmap bitmap, int roundPixels) {
        try {
            if (bitmap == null) {
                return null;
            }
            /**创建一个和原始图片一样大小位图*/
            Bitmap roundConcerImage = Bitmap.createBitmap(bitmap.getWidth(),
                    bitmap.getHeight(), Bitmap.Config.ARGB_8888);
            /**创建带有位图roundConcerImage的画布*/
            Canvas canvas = new Canvas(roundConcerImage);
            /**创建画笔  */
            Paint paint = new Paint();
            /**创建一个和原始图片一样大小的矩形*/
            Rect rect = new Rect(0, 0, bitmap.getWidth(), bitmap.getHeight());
            RectF rectF = new RectF(rect);
            /**去锯齿*/
            paint.setAntiAlias(true);
            /**画一个和原始图片一样大小的圆角矩形*/
            canvas.drawRoundRect(rectF, roundPixels, roundPixels, paint);
            /**设置相交模式  */
            paint.setXfermode(new PorterDuffXfermode(PorterDuff.Mode.SRC_IN));
            /**把图片画到矩形去  */
            canvas.drawBitmap(bitmap, rect, rectF, paint);

            /**引时圆角区域为透明，给其填充白色  */
            paint.setColor(Color.WHITE);
            paint.setXfermode(new PorterDuffXfermode(PorterDuff.Mode.DST_ATOP));
            canvas.drawRect(rectF, paint);
            return roundConcerImage;
        } catch (Exception e) {
            return null;
        }
    }

    public static void onShareResult(String channel, int result) {
        String tip = shareResultTip;
        // 1 成功  -1 失败 -2 取消
        if (result == 1 && channel != null && tip != null && !tip.isEmpty()) {
            Toaster.showToast(tip);
        }
        shareResultTip = null;
    }

    private static void trackShareSystem(String targetType, String shareLanguage) {
        Map<String, Object> ctx = new HashMap<>();
        ctx.put("share_language", shareLanguage == null ? LanguageManager.get().getLanguage() : shareLanguage);
        AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                .setMod_nm(EagleTrackEvent.ModNm.SYSTEM_POPUP)
                .setTargetNm(targetType)
                .setTargetType(EagleTrackEvent.TargetType.SHARE_CHANNEL)
                .setClickType(EagleTrackEvent.ClickType.SHARE)
                .addCtx(ctx)
                .build().getParams());
    }

    /**
     * 分享到 Kakao
     */
    public static void shareKakao(Context context, final String url) {
        if (ShareClient.getInstance().isKakaoTalkSharingAvailable(context)) {
            WrapperDialog loadingDialog = LoadingProvider.get().create(getActivity());
            if (loadingDialog != null) {
                loadingDialog.show();
            }
            ShareClient.getInstance().shareScrap(context, url, new Function2<SharingResult, Throwable, Unit>() {
                @Override
                public Unit invoke(SharingResult sharingResult, Throwable error) {
                    if (sharingResult != null) {
                        context.startActivity(sharingResult.getIntent());
                    } else if (error != null && DevConfig.isDevelop()) {
                        Toaster.showToast(error.getMessage());
                    }
                    if (loadingDialog != null) {
                        loadingDialog.dismiss();
                    }
                    return null;
                }
            });
        } else {
            // If Kakao Talk is not installed, it is recommended to share URI via web.`
            Uri uri = WebSharerClient.getInstance().makeScrapUrl(url);
            try {
                KakaoCustomTabsClient.INSTANCE.openWithDefault(context, uri);
            } catch (UnsupportedOperationException ignored) {

            }
        }
    }

    private static boolean shareFacebookImage(Bitmap bitmap, ShareCallback callback) {
        if (haveFacebookGroup()) {
            showFacebookDialogImage(bitmap);
        } else {
            shareFacebookFeedImage(bitmap);
        }
        dispatchShareResult(callback, Constants.ShareChannel.FACEBOOK, true, null);
        return true;
    }

    private static void showFacebookDialogImage(Bitmap bitmap) {
        new WrapperDialog(getActivity(), R.style.BottomDialogTheme) {
            @Override
            public void help(ViewHelper helper) {
                helper.setOnClickListener(new OnSafeClickListener() {
                    @Override
                    public void onClickSafely(View v) {
                        dismiss();
                        if (v.getId() == R.id.tv_facebook_feed) {
                            shareFacebookFeedImage(bitmap);
                        } else if (v.getId() == R.id.tv_facebook_group) {
                            shareFacebookGroupImage(bitmap);
                        }
                    }
                }, R.id.tv_facebook_feed, R.id.tv_facebook_group, R.id.tv_cancel);
            }

            @Override
            protected int getLayoutRes() {
                return R.layout.dialog_facebook_popup;
            }

            @Override
            protected void setDialogParams(Dialog dialog) {
                setDialogParams(dialog, WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.WRAP_CONTENT, Gravity.BOTTOM);
            }
        }.show();
    }

    private static void shareFacebookFeedImage(Bitmap bitmap) {
        SharePhotoContent sharePhotoContent = new SharePhotoContent.Builder()
                .addPhoto(new SharePhoto.Builder().setBitmap(bitmap).build())
                .build();

        ShareDialog shareDialog = new ShareDialog(getActivity());
        if (callbackManager == null) {
            callbackManager = CallbackManager.Factory.create();
        }
        shareDialog.registerCallback(callbackManager, new FacebookCallback<Sharer.Result>() {
            @Override
            public void onSuccess(Sharer.Result result) {
                onShareResult(Constants.ShareChannel.FACEBOOK, 1);
            }

            @Override
            public void onCancel() {
                // no op
            }

            @Override
            public void onError(@NonNull FacebookException error) {
                // no op
            }
        });
        shareDialog.show(sharePhotoContent);
    }

    private static void shareFacebookGroupImage(Bitmap bitmap) {
        Intent shareIntent = createShareImageIntent(bitmap);
        if (shareIntent == null) {
            return;
        }

        PackageManager pm = getActivity().getPackageManager();
        List<ResolveInfo> activityList = pm.queryIntentActivities(shareIntent, 0);
        for (final ResolveInfo app : activityList) {
            if (EmptyUtils.isEmpty(app.activityInfo) || EmptyUtils.isEmpty(app.activityInfo.name) || EmptyUtils.isEmpty(app.activityInfo.packageName)) {
                continue;
            }
            if ((app.activityInfo.packageName).contains("com.facebook.katana") && (app.activityInfo.name).contains("Groups")) {
                final ActivityInfo activity = app.activityInfo;
                final ComponentName component = new ComponentName(
                        activity.applicationInfo.packageName,
                        activity.name
                );
                shareIntent.addCategory(Intent.CATEGORY_LAUNCHER);
                shareIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                shareIntent.setComponent(component);
                startActivity(Intent.createChooser(shareIntent, "share"));
                return;
            }
        }
    }

    private static boolean shareWechatImage(Bitmap bitmap) {
        return shareWechatImage(SendMessageToWX.Req.WXSceneSession, bitmap);
    }

    private static boolean shareWechatImageMoment(Bitmap bitmap) {
        return shareWechatImage(SendMessageToWX.Req.WXSceneTimeline, bitmap);
    }

    private static boolean shareWechatImage(int scene, Bitmap bitmap) {
        IWXAPI api = WXAPIFactory.createWXAPI(LifecycleProvider.get().getApplication(), AppConfig.KEY_WX_ID, false);
        WXImageObject imgObj = new WXImageObject(bitmap);
        WXMediaMessage msg = new WXMediaMessage(imgObj);
        SendMessageToWX.Req req = new SendMessageToWX.Req();
        req.transaction = buildTransaction("img");
        req.message = msg;
        req.scene = scene;
        api.sendReq(req);
        return true;
    }

    public static boolean shareMessengerImage(Bitmap bitmap) {
        return shareImageBySystem(bitmap, "com.facebook.orca");
    }

    public static boolean shareInstagramImage(Bitmap bitmap) {
        return shareImageBySystem(bitmap, "com.instagram.android");
    }

    public static boolean shareWhatsAppImage(Bitmap bitmap) {
        return shareImageBySystem(bitmap, "com.whatsapp");
    }

    public static boolean shareKakaoImage(Bitmap bitmap) {
        return shareImageBySystem(bitmap, "com.kakao.talk");
    }

    private static boolean shareImageBySystem(Bitmap bitmap, String packageName) {
        if (bitmap != null && packageName != null) {
            Intent intent = createShareImageIntent(bitmap);
            if (intent != null) {
                intent.setPackage(packageName);
                return startActivity(intent);
            }
        }
        return false;
    }

    public static void dispatchShareResult(ShareCallback callback, String channel, boolean result, String data) {
        if (callback != null) {
            callback.onResult(channel, result, data);
        }
    }

    public static void cleanupSharedImages(@NonNull Context context) {
        long now = System.currentTimeMillis();
        long maxDiffMillis = TimeUnit.DAYS.toMillis(3);
        cleanupSharedImages(context, (dir, name) -> {
            int posStart = name.indexOf(SHARE_IMAGES_PREFIX);
            int posEnd = name.lastIndexOf(SHARE_IMAGES_EXT);
            if (posStart >= 0 && posEnd >= 0) {
                String millis = name.substring(posStart + SHARE_IMAGES_PREFIX.length(), posEnd);
                long m = DecimalTools.longValue(millis, 0L);
                return m > 0L && (now - m) > maxDiffMillis;
            }
            return false;
        });
    }

    private static void cleanupSharedImages(@NonNull Context context, @NonNull FilenameFilter filter) {
        if (sSharedImageCleanup) {
            return;
        }
        sSharedImageCleanup = true;
        Runnable job = () -> {
            File dir = new File(context.getExternalCacheDir(), SHARE_IMAGES_DIR);
            if (!dir.isDirectory()) {
                return;
            }
            File[] files = dir.listFiles(filter);
            if (files == null) {
                return;
            }
            for (File file : files) {
                FileUtils.deleteFile(file);
            }
        };
        Completable.fromRunnable(job)
                .subscribeOn(Schedulers.io())
                .observeOn(Schedulers.io())
                .subscribe(new SimpleCompletableObserver());
    }

    public static ShareBean getShareBean() {
        ShareBean bean = new ShareBean();
        bean.share_channels = new ArrayList<>(Arrays.asList(
                Constants.ShareChannel.WECHAT,
                Constants.ShareChannel.WECHAT_MOMENT,
                Constants.ShareChannel.FACEBOOK,
                Constants.ShareChannel.MESSENGER,
                Constants.ShareChannel.INSTAGRAM,
                Constants.ShareChannel.WHATSAPP,
                Constants.ShareChannel.KAKAO,
                Constants.ShareChannel.LINE,
                Constants.ShareChannel.MORE,
                Constants.ShareChannel.COPY_LINK,
                Constants.ShareChannel.SMS,
                Constants.ShareChannel.EMAIL
        ));
        return bean;
    }
}
