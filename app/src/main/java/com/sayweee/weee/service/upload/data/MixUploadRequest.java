package com.sayweee.weee.service.upload.data;

import java.util.List;

public final class MixUploadRequest {

    public final String bizType;
    public final List<Item> data;

    public MixUploadRequest(String bizType, List<Item> data) {
        this.bizType = bizType;
        this.data = data;
    }

    public static class Item {

        public final String subType;
        public final List<String> files;

        public Item(String subType, List<String> files) {
            this.subType = subType;
            this.files = files;
        }
    }

}
