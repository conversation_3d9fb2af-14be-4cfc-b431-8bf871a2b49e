package com.sayweee.weee.service.upload;

import com.sayweee.weee.service.upload.data.MixUploadResponse;

import io.reactivex.Observable;
import okhttp3.MultipartBody;
import retrofit2.http.*;

import java.util.List;

public interface ResourceApi {

    @Multipart
    @POST
    Observable<MixUploadResponse> mixUpload(
            @Url String url,
            @Header("Authorization") String authorization,
            @Part List<MultipartBody.Part> partList
    );
}
