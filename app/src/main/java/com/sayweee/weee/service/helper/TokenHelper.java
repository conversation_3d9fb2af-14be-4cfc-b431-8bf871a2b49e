package com.sayweee.weee.service.helper;

import android.text.TextUtils;

import com.sayweee.service.SessionService;
import com.sayweee.weee.global.config.VariantConfig;
import com.sayweee.weee.global.manager.OrderManager;
import com.sayweee.weee.module.account.bean.SimplePreOrderBean;
import com.sayweee.weee.module.launch.service.LaunchApi;
import com.sayweee.weee.module.shared.SharedViewModel;
import com.sayweee.wrapper.bean.ResponseBean;
import com.sayweee.wrapper.core.compat.ResponseObserver;
import com.sayweee.wrapper.core.compat.ResponseTransformer;
import com.sayweee.wrapper.http.RetrofitIml;
import com.sayweee.wrapper.http.support.RequestParams;

/**
 * Author:  winds
 * Date:    2022/10/25.
 * Desc:
 */
public class TokenHelper {

    //生成匿名token
    public static void createAnonymousToken() {
        RequestParams requestParams = new RequestParams();
        requestParams.put("auth_token_channel", VariantConfig.AUTH_TOKEN_CHANNEL);
        SessionService.get().fetchToken(true, requestParams.get(), (token, failureBean) -> {
            if (token != null) {
                SharedViewModel.get().refreshAccountInfo();
                createPreOrder();
            }
        });
    }

    private static void createPreOrder() {
        String oldCode = OrderManager.get().getZipCode();
        createPreOrder(TextUtils.isEmpty(oldCode) ? OrderManager.DEFAULT_ZIP_CODE : oldCode);
    }

    private static void createPreOrder(String code) {
        RetrofitIml.get().getHttpService(LaunchApi.class)
                .createPreOrder(new RequestParams().put("zipcode", code).create())
                .compose(ResponseTransformer.scheduler())
                .subscribe(new ResponseObserver<ResponseBean<SimplePreOrderBean>>() {

                    @Override
                    public void onResponse(ResponseBean<SimplePreOrderBean> response) {
                        SimplePreOrderBean data = response.getData();
                        OrderManager.get().setSimpleOrderData(data);
                    }

                });
    }

}
