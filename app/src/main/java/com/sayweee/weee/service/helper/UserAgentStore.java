package com.sayweee.weee.service.helper;

import androidx.annotation.NonNull;

import com.sayweee.weee.BuildConfig;
import com.sayweee.weee.global.mmkv.MMKVManager;
import com.sayweee.weee.service.rx.SimpleCompletableObserver;
import com.sayweee.weee.utils.function.Consumer;
import com.sayweee.wrapper.core.compat.SimpleObserver;

import io.reactivex.Completable;
import io.reactivex.Observable;
import io.reactivex.disposables.Disposable;

public final class UserAgentStore {

    private static final String CONFIG_KEY_PREFIX = "user_agent_webview_";

    private UserAgentStore() {

    }

    public static void obtainFromLocal(@NonNull Consumer<String> onResult) {
        rxObtainFromLocal().subscribe(new SimpleObserverImpl<String>() {
            @Override
            public void onNext(String o) {
                super.onNext(o);
                onResult.accept(o);
            }

            @Override
            public void onError(Throwable e) {
                super.onError(e);
                onResult.accept("");
            }
        });
    }

    public static void saveToLocal(@NonNull String userAgent) {
        rxSaveToLocal(userAgent).subscribe(new SimpleCompletableObserver());
    }

    private static Completable rxSaveToLocal(@NonNull String userAgent) {
        return Completable.fromAction(() -> {
            MMKVManager.obtain(MMKVManager.ID_CONFIG).encode(getConfigKey(), userAgent);
        });
    }

    private static Observable<String> rxObtainFromLocal() {
        return Observable.fromCallable(() -> MMKVManager.obtain(MMKVManager.ID_CONFIG).decodeString(getConfigKey(), ""));
    }

    @NonNull
    private static String getConfigKey() {
        return CONFIG_KEY_PREFIX + BuildConfig.VERSION_NAME + "_" + BuildConfig.VERSION_CODE;
    }

    private static class SimpleObserverImpl<T> extends SimpleObserver<T> {

        private Disposable disposable;

        @Override
        public void onSubscribe(Disposable d) {
            disposable = d;
        }

        @Override
        public void onComplete() {
            if (!disposable.isDisposed()) {
                disposable.dispose();
            }
        }
    }
}
