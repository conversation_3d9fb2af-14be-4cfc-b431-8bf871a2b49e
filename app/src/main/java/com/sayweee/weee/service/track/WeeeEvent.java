package com.sayweee.weee.service.track;

/**
 * Author:  winds
 * Email:   <EMAIL>
 * Date:    2020/8/29.
 * Desc:    app埋点
 * 见https://docs.google.com/spreadsheets/d/1DVNaJjRc63j43uABjwifQaVpf49A93mMKBr-6dUDBUg/edit#gid=685507302
 * 标注 >>web 是指事件由web处理
 * {xx:xx}，是指事件需要上报参数，由:标示
 */
public class WeeeEvent {

    //platform
    public final static String TYPE_WECHAT = "weixin";
    public final static String TYPE_FACEBOOK = "facebook";
    public final static String TYPE_GOOGLE = "google";
    public final static String TYPE_KAKAO = "kakao";
    public final static String TYPE_EMAIL = "email";
    public final static String TYPE_PHONE = "phone";
    public final static String TYPE_APPLE = "apple";
    public final static String TYPE_LINE = "line";
    public final static String TYPE_TIKTOK = "tiktok";


    // popup event
    public static final String POPUP_PAGE_GLOBAL_LANDING = "page_global_landing";
    public static final String POPUP_PAGE_MKPL_WATERFALL = "page_mkpl_waterfall";
    public static final String POPUP_PAGE_BAKERY_LANDING = "page_bakery_landing";


    //pv
    //邮箱验证
    @Deprecated
    public static final String EVENT_PAGE_LOGIN_ACCOUNT_VERIFY = "login_account_verify";
    //注册
    @Deprecated
    public static final String EVENT_PAGE_REGISTER = "page_signup";
    //绑定账号
    @Deprecated
    public static final String EVENT_PAGE_CONNECT_ACCOUNT = "page_connect_account";
    //合集(shopping_list/collect) >> web
    @Deprecated
    public static final String EVENT_PAGE_COLLECTION = "page_collection";
    //重设密码
    @Deprecated
    public static final String EVENT_PAGE_RESET_PASSWORD = "page_reset_password";
    //onboarding选择语言 app主动上报 为dialog
    @Deprecated
    public static final String EVENT_PAGE_ON_BOARDING_LANGUAGE = "page_onboarding_language";
    //onboarding插画介绍 app主动上报 和onboarding同key
    @Deprecated
    public static final String EVENT_PAGE_ON_BOARDING_CAROUSEL = "page_onboarding_carousel";
    //onboarding输入邮编
    @Deprecated
    public static final String EVENT_PAGE_ON_BOARDING_ZIP_CODE = "page_onboarding_zipcode";
    //onboarding订阅
    @Deprecated
    public static final String EVENT_PAGE_ON_BOARDING_SUBSCRIPTION = "page_onboarding_subscription";
    //修改日期 app主动上报 和切换送货日同key
    @Deprecated
    public static final String EVENT_PAGE_CHANGE_DATE = "page_change_date";
    @Deprecated
    public static final String EVENT_PAGE_CHANGE_DATE_V2 = "change_date_view";
    //bogo list PV埋點
    @Deprecated
    public static final String EVENT_PAGE_BOGO_COLLECT = "page_bogo_collect";
    //bundle list页面 >> web
    @Deprecated
    public static final String EVENT_PAGE_BUNDLE_LIST = "page_bundle_list";
    //bundle详情页 >> web
    @Deprecated
    public static final String EVENT_PAGE_BUNDLE_DETAIL = "page_bundle_detail";
    //购物车换购页面 >> web
    @Deprecated
    public static final String EVENT_PAGE_TRADE_IN = "page_trade_in";
    //onboarding选择cuisine
    @Deprecated
    public static final String EVENT_PAGE_ON_BOARDING_CUISINE = "page_onboarding_cuisine";
    //bundle >> web
    @Deprecated
    public static final String EVENT_PAGE_BUNDLE = "page_bundle";
    //订单确认-选择优惠券 >> web
    @Deprecated
    public static final String EVENT_PAGE_CHECKOUT_COUPON = "page_checkout_coupon";
    @Deprecated
    public static final String EVENT_PAGE_CHECKOUT_UPSELL = "page_checkout_upsell";
    //定义：用户查community page时记录
    @Deprecated
    public static final String EVENT_PAGE_POST_LIST = "page_post_list";
    @Deprecated
    public static final String EVENT_PAGE_POST_WITHIN_PDP = "page_post_within_pdp";
    @Deprecated
    public static final String EVENT_PAGE_EDIT_VIDEO = "page_post_edit_video";
    @Deprecated
    public static final String EVENT_PAGE_DETAIL = "page_post_detail";
    // 商家页面pv 上报前缀
    @Deprecated
    public static final String EVENT_PAGE_VENDOR = "page_vendor_";
    //filter view事件
    @Deprecated
    public static final String EVENT_PAGE_FILTER_VIEW = "filter_view";
    //filter click 埋点
    @Deprecated
    public static final String EVENT_FILTER_APPLY = "filter_apply";
    //action
    //登陆页面
    @Deprecated
    public static final String EVENT_LOGIN_INDEX_PAGE_VIEW = "login_index_page_view";
    //点击登录选项
    @Deprecated
    public static final String EVENT_LOGIN_BTN_CLICK = "login_btn_click";
    //账号绑定事件
    @Deprecated
    public static final String EVENT_LOGIN_ACCOUNT_BIND_VIEW = "login_account_bind_view";
    //language: 当前语言
    @Deprecated
    public static final String EVENT_ON_BOARDING_LANGUAGE_OPTION_SELECT = "onboarding_language_option_select";
    //
    @Deprecated
    public static final String EVENT_ON_BOARDING_CAROUSAL_START_CLICK = "onboarding_carousal_start_click";
    //
    @Deprecated
    public static final String EVENT_ON_BOARDING_CAROUSAL_LOGIN_CLICK = "onboarding_carousal_login_click";
    //onboarding在注册界面跳过
    @Deprecated
    public static final String EVENT_ON_BOARDING_CAROUSAL_SIGN_UP_SKIP = "onboarding_carousal_signup_skip";
    //zipcode: 输入zipcode
    @Deprecated
    public static final String EVENT_ON_BOARDING_ZIP_CODE_CONFIRM_CLICK = "onboarding_zipcode_confirm_click";
    //使用自动定位
    @Deprecated
    public static final String EVENT_ON_BOARDING_ZIP_CODE_LOCATION_CLICK = "onboarding_zipcode_location_click";
    //subscribe界面点击订阅
    @Deprecated
    public static final String EVENT_ON_BOARDING_SUBSCRIBE_CTA_CLICK = "onboarding_subscribe_cta_click";
    //使用默认邮编浏览
    @Deprecated
    public static final String EVENT_ON_BOARDING_THANKS_CTA_CLICK = "onboarding_thanks_cta_click";
    //category: 商品分类 sort：排序
    @Deprecated
    public static final String EVENT_PRODUCT_LIST_SORT_CHANGE = "product_list_sort_change";
    //product_id: 商品ID http_refer:
    @Deprecated
    public static final String EVENT_PRODUCT_VIEW_LOAD = "product_view_load";
    //type: banner类型，有四种
    @Deprecated
    public static final String EVENT_PRODUCT_DETAIL_BANNER_CLICK = "product_detail_banner_click";
    //滑动商品图片 index：image索引 product_id：商品ID  展示时触发，滑动一次触发一次
    @Deprecated
    public static final String EVENT_PRODUCT_DETAIL_PAGE_CAROUSEL_VIEW = "product_detail_page_carousel_view";
    //点击商品图片 index：image索引 product_id：商品ID
    @Deprecated
    public static final String EVENT_PRODUCT_DETAIL_PAGE_CAROUSEL_CLICK = "product_detail_page_carousel_click";
    //section: post/recommend  在可视区域时触发
    @Deprecated
    public static final String EVENT_PRODUCT_DETAIL_SECTION_IMPRESSION = "product_detail_section_impression";
    //product_id: 商品ID
    @Deprecated
    public static final String EVENT_PRODUCT_DETAIL_GO_TO_CART_CLICK = "product_detail_gotocart_click";
    //product_id: bundle商品ID
    @Deprecated
    public static final String EVENT_BUNDLE_LIST_GO_TO_CART_CLICK = "bundle_list_gotocart_click";
    //product_id: bundle商品ID
    @Deprecated
    public static final String EVENT_BUNDlE_LIST_PRODUCT_CLICK = "bundle_list_product_click";
    //product_id: bundle商品ID
    @Deprecated
    public static final String EVENT_BUNDLE_DETAIL_GO_TO_CART_CLICK = "bundle_detail_gotocart_click";
    //product_id: bundle商品ID
    @Deprecated
    public static final String EVENT_BUNDLE_DETAIL_PRODUCT_CLICK = "bundle_detail_product_click";
    //value: 商品ID page: 页面来源
    @Deprecated //app现在不报这个事件 接口自处理
    public static final String EVENT_PRODUCT_FAVORITE_ADD = "product_favorite_add";
    //？？  >> web
    @Deprecated
    public static final String EVENT_SHARE_CLICK = "share_click";
    //section: bought/recommend
    @Deprecated
    public static final String EVENT_CART_SECTION_IMPRESSION = "cart_section_impression";
    //section: grocery/bundle
    @Deprecated
    public static final String EVENT_CART_CHECKOUT_CLICK = "cart_checkout_click";
    //点击展开
    @Deprecated
    public static final String EVENT_CART_UNAVAILABLE_SHOW_MORE_CLICK = "cart_unavailable_show_more_click";
    //点击更改日期
    @Deprecated
    public static final String EVENT_CART_UNAVAILABLE_CHANGE_DATE_CLICK = "cart_unavailable_change_date_click";
    //
    @Deprecated
    public static final String EVENT_CHECKOUT_CHANGE_DATE_TIME_CLICK = "checkout_change_date_time_click";
    // >> web
    @Deprecated
    public static final String EVENT_CHECKOUT_CHANGE_DELIVERY_INFO_CLICK = "checkout_change_delivery_info_click";
    // >> web
    @Deprecated
    public static final String EVENT_CHECKOUT_CHANGE_DELIVERY_INFO_CONFIRM = "checkout_change_delivery_info_confirm";
    //confirm: 1/0  >> web
    @Deprecated
    public static final String EVENT_CHECKOUT_CHANGE_DELIVERY_INFO_SUCCESS = "checkout_change_delivery_info_success";
    // >> web
    @Deprecated
    public static final String EVENT_CHECKOUT_CHANGE_PAYMENT_CLICK = "checkout_change_payment_click";
    // >> web
    @Deprecated
    public static final String EVENT_CHECKOUT_ORDER_ITEMS_CLICK = "checkout_order_items_click";
    //page: order_items/checkout state: success/failure  >> web
    @Deprecated
    public static final String EVENT_CHECKOUT_PURCHASE_CLICK = "checkout_purchase_click";
    //首页点击修改zipcode
    @Deprecated
    public static final String EVENT_HOME_ZIP_CODE_CLICK = "home_zipcode_click";
    //首页点击修改日期
    @Deprecated
    public static final String EVENT_HOME_DATE_CLICK = "home_date_click";
    //首页点击消息short title，展开消息
    @Deprecated
    public static final String EVENT_HOME_SHORT_MESSAGE_CLICK = "home_short_message_click";
    //POST REVIEW PROFILE分享
    @Deprecated
    public static final String EVENT_POST_SHARE_CLICK = "post_share_click";
    //用户在change date的页面点击
    @Deprecated
    public static final String EVENT_CHANGE_DATE_CLICK = "change_date_click";

    //module: 模块枚举定义
    //url: 跳转的URL，如果跳转原生页面，需要传对应的H5页面的URL
    //extra: 附加参数 - 如果是菜系，传菜系的key，例如chinese
    //
    // Module模块定义
    //cm_search - 顶部搜索按钮
    //cm_top_message - 长消息
    //cm_notice_banner - 广告位
    //cm_main_banner - 主banner
    //cm_categories - 分类入口
    //cm_bundles - bundle分类入口
    //cm_feature - 特色专区
    //cm_lightning_deals - 限时秒杀
    //cm_deal_of_week - 一周一品
    //cm_collection - 合集
    //cm_share_banner - 邀请banner
    //cm_recommend-preference - 为你推荐
    //cm_recommend-post- 最多晒单
    //cm_recommend-breakfast - 元气早餐
    //cm_cuisines - 菜系列表
    //cm_item_line - 英文模式为你推荐
    @Deprecated
    public static final String EVENT_HOME_OUT_CLICK = "home_out_click";
    //点击退出登录 >> web
    @Deprecated
    public static final String EVENT_LOGIN_LOGOUT_SUCCESS = "login_logout_success";
    //商品列表页显示模式切换 type: list/card
    @Deprecated
    public static final String EVENT_PRODUCT_LIST_SHOW_MODE_CHANGE = "product_list_showmode_change";
    //url: Popup页面打开的URL  >> web
    @Deprecated
    public static final String EVENT_POPUP_VIEW_GENERIC = "popup_view_generic";
    //App显示推送引导框
    @Deprecated
    public static final String EVENT_PUSH_PERMISSION_POPUP_VIEW = "push_permission_popup_view";
    //App显示推送引导框Button点击  action: 1 - 允许推送； 0 - 稍后再说
    @Deprecated
    public static final String EVENT_PUSH_PERMISSION_POPUP_CLICK = "push_permission_popup_click";
    //status: 1 - 打开； 0 - 关闭
    @Deprecated
    public static final String EVENT_OS_NOTIFICATION_STATUS = "os_notification_status";
    //1. 模块出现在可视区超过1秒，才上报impression事件
    //2. 同一个模块只在第一次显示上报
    //3. 从其他tab切回首页，需要重新上报impression事件
    //4. 合集impression上报，需要带上合集索引值
    //module: 模块枚举定义
    //page: 模块内容URL
    //extra:
    // - 如果是合集，需要传合集索引
    //Module模块定义
    //
    //
    //cm_top_message - 长消息
    //cm_notice_banner - 广告位
    //cm_main_banner - 主banner
    //cm_categories - 分类入口
    //cm_bundles - bundle分类入口
    //cm_feature - 特色专区
    //cm_lightning_deals - 限时秒杀
    //cm_deal_of_week - 一周一品
    //cm_collection - 合集
    //cm_share_banner - 邀请banner
    //cm_recommend-preference - 为你推荐
    //cm_recommend-post- 最多晒单
    //cm_recommend-breakfast - 元气早餐
    //
    //cm_cuisines - 菜系列表
    //cm_item_line - 英文模式为你推荐
    @Deprecated
    public static final String EVENT_HOME_IMPRESSION = "home_impression";
    //key : preference/post/breakfast
    @Deprecated
    public static final String EVENT_HOME_RECOMMEND_IMPRESSION = "home_recommend_impression";
    //module: 模块枚举定义
    //page: 模块内容URL
    //extra: 如果是合集，需要传合集索引
    //Module模块定义
    //cm_main_banner - 主banner
    //cm_categories - 分类入口
    //cm_bundles - bundle分类入口
    //cm_feature - 特色专区
    //cm_lightning_deals - 限时秒杀
    //cm_collection - 合集
    //cm_cuisines - 菜系列表
    //cm_item_line - 英文模式为你推荐
    @Deprecated
    public static final String EVENT_HOME_MODULE_SWIPE = "home_module_swipe";
    //onboarding跳过cuisine选择
    @Deprecated
    public static final String EVENT_ON_BOARDING_CUISINE_SKIP_CLICK = "onboarding_cuisine_skip_click";
    //key: cuisine name
    // - 例如chinese, korean
    //position: cuisine index, from 0
    @Deprecated
    public static final String EVENT_HOME_CUISINE_IMPRESSION = "home_cuisine_impression";
    //https://docs.google.com/spreadsheets/d/1DVNaJjRc63j43uABjwifQaVpf49A93mMKBr-6dUDBUg/edit#gid=685507302
    @Deprecated
    public static final String EVENT_ME_OUT_CLICK = "me_out_click";

    //订单完成页查看订单详情
    //订单完成页继续购物
    //订单完成页回到购物车
    //action: view_details/continue_shopping/back_to_cart  >> web
    @Deprecated
    public static final String EVENT_CART_SUCCESS_LEAVE_CLICK = "cart_success_leave_click";
    //加购归因
    @Deprecated
    public static final String EVENT_CART_ADD_TO_CART_FRONT = "cart_add_to_cart_front";//
    @Deprecated
    public static final String PAGE_SEARCH = "page_search";//
    @Deprecated
    public static final String PAGE_PRODUCT_DETAILS = "page_product_details";//
    @Deprecated
    public static final String PAGE_PRODUCT_DETAILS_RELATED = "page_product_details_related";//
    @Deprecated
    public static final String SEARCH_SOURCE = "search_";//
    @Deprecated
    public static final String RELATED_LIST_SOURCE = "related-list_";//
    @Deprecated
    public static final String RELATED_PDP_SOURCE = "related-pdp_";//
    @Deprecated
    public static final String POST_SOURCE = "post_detail-";//post_detail-post id

    //position type
    @Deprecated
    public static final String EVENT_CAROUSEL_VIEW = "carousel_view";
    @Deprecated
    public static final String EVENT_CAROUSEL_CLICK = "carousel_click";
    //recommend_product_id  source_product_id  列表页推荐
    @Deprecated
    public static final String EVENT_PRODUCT_RECOMMEND_SHOPPING_LIST_CHOOSE = "product_recommend_shopping_list_choose";
    //recommend_product_id  source_product_id
    @Deprecated
    public static final String EVENT_PRODUCT_RECOMMEND_PRODUCT_CHOOSE = "product_recommend_product_choose";
    //recommend_product_id  source_product_id
    @Deprecated
    public static final String EVENT_PRODUCT_BUY_TOGETHER_PRODUCT_CHOOSE = "product_buy_together_product_choose";
    //product_id  location：(banner-click right-icon-share menuitem-share)
    @Deprecated
    public static final String EVENT_PRODUCT_DETAIL_PAGE_SHARE_POPUP = "product_detail_page_share_popup";
    //page ：(portal cart search)  product_id  滑动时上报从下往上显示的商品
    @Deprecated
    public static final String EVENT_PREFERENCE_VIEW = "preference_view";
    //
    @Deprecated
    public static final String EVENT_BOUGHT_BEFORE_VIEW = "bought_before_view";
    //channel_type  - email,facebook,kakao,phone,weixin,apple
    //user_id - 用户的user id
    @Deprecated
    public static final String EVENT_SIGN_UP = "sign_up";

    //add_to_cart
    @Deprecated
    public static final String EVENT_ADD_TO_CART = "add_to_cart";
    //product_id  category: 小类 例如green01 bundle特殊，为定值bundle
    @Deprecated
    public static final String EVENT_SHOPPING_LIST_IMPRESSION = "shopping_list_impression";
    @Deprecated
    public static final String EVENT_VIDEO_VIEW_PRODUCT_IMPRESSION = "video_view_product_impression";
    //trace_id  product_id  params
    @Deprecated
    public static final String EVENT_SEARCH_IMPRESSION = "search_impression";
    //1) type:   install - 安装App;  open - 打开App2) af_id:   Appsflyer生成的ID3) af_url:   打开App使用的Appsflyer链接
    @Deprecated
    public static final String EVENT_APPSFLYER_CALLBACK = "appsflyer_callback";
    @Deprecated
    public static final String EVENT_EMPTY_SEARCH_SUGGEST_VIEW = "empty_search_suggest_view";
    @Deprecated
    public static final String EVENT_EMPTY_SEARCH_SUGGEST_SUBMIT = "empty_search_suggest_submit";
    //me page   section :
    @Deprecated
    public static final String EVENT_ME_PAGE_IMPRESSION = "me_page_impression";

    //home track
    @Deprecated
    public static final String EVENT_HOME_PRODUCT_IMPRESSION = "home_product_impression";
    //upSell
    @Deprecated
    public static final String EVENT_CHECKOUT_UPSELL_CLICK = "checkout_upsell_click";
    //upSell impression
    @Deprecated
    public static final String EVENT_CHECKOUT_UPSELL_VIEW = "checkout_upsell_view";
    //home post impression
    @Deprecated
    public static final String EVENT_HOME_POST_IMPRESSION = "home_post_impression";
    //checkout 会员试用查看  session_id， user_id
    @Deprecated
    public static final String EVENT_VIP_TRIAL_CHECKOUT_POPUP_VIEW = "vip_trial_checkout_popup_view";
    //checkout 会员试用确认  session_id， user_id
    @Deprecated
    public static final String EVENT_VIP_TRIAL_CHECKOUT_POPUP_CLICK = "vip_trial_checkout_popup_click";
    //onboarding_carousel_swipe_view
    @Deprecated
    public static final String EVENT_ONBOARDING_CAROUSEL_SWIPE_VIEW = "onboarding_carousel_swipe_view";
    //Data tracking for Continuous NPS
    @Deprecated
    public static final String NPS_VIEW = "nps_view";
    @Deprecated
    public static final String NPS_CLICK = "nps_click";
    //Visiting Profile pages
    @Deprecated
    public static final String USER_PROFILE_PAGE_CLICK = "user_profile_page_click";
    @Deprecated
    public static final String USER_PROFILE_PAGE_VIEW = "user_profile_page_view";


    //post
    //post or review detail click
    public static final String EVENT_POST_DETAIL_CLICK = "post_detail_click"; //保留
    //用户点击pdp页面的post列表中某一个post进入post_detail
    @Deprecated
    public static final String EVENT_POST_WITHIN_PDP_CLICK = "post_within_pdp_click";
    //"Post impression ：--> metrics.post_list_impression
    public static final String EVENT_POST_LIST_IMPRESSION = "post_list_impression"; //保留
    //"用户在post list page 点击clickable 上报
    public static final String EVENT_POST_LIST_CLICK = "post_list_click"; //保留
    //Community following tab post card impression 社区follow页面帖子曝光
    @Deprecated
    public static final String EVENT_POST_FOLLOWING_LIST_IMPRESSION = "post_following_list_impression";
    //Community following tab click 社区follow页面点击事件 - click
    @Deprecated
    public static final String EVENT_POST_FOLLOWING_LIST_CLICK = "post_following_list_click";
    //Video Browse: 用户向上向下滑动视频进行切换
    @Deprecated
    public static final String EVENT_POST_VIDEO_SWIPE = "post_video_swipe";
    //Tracking for video post consumption:
    public static final String EVENT_POST_VIDEO_CONSUMPTION = "post_video_consumption"; //保留
    //回退到h5 hashtag页面埋点
    @Deprecated
    public static final String EVENT_POST_HASHTAG_DETAIL_VIEW = "post_hashtag_detail_view";
    @Deprecated
    public static final String EVENT_POST_HASHTAG_LIST_VIEW = "post_hashtag_list_view";
    //Community following tab profile carousel impression
    @Deprecated
    public static final String EVENT_POST_FOLLOWING_LIST_PROFILE_CAROUSEL_IMPRESSION = "post_following_list_profile_carousel_impression";
    //Community following tab profile card impression
    @Deprecated
    public static final String EVENT_POST_FOLLOWING_LIST_PROFILE_CARD_IMPRESSION = "post_following_list_profile_card_impression";
    //首页商品点击
    @Deprecated
    public static final String EVENT_HOME_PRODUCT_CLICK = "home_product_click";
    //preference_click
    @Deprecated
    public static final String EVENT_PREFERENCE_CLICK = "preference_click";

    //theme
    //home_page_module 从主页的theme模块进入
    //theme_detail_back 从theme详情页返回
    //theme_top_click theme列表页顶部点击l1 tag切换
    //theme_list_swipe theme列表页横向滑动切换
    //theme_list_explore从theme详情页底部点击任意l1主题卡片切换
    //外部分享email，push，share等（利用URL参数ws=）
    @Deprecated
    public static final String EVENT_THEME_LIST_PAGE_PV = "theme_list_page_pv";
    //
    @Deprecated
    public static final String EVENT_THEME_LIST_PAGE_CLICK = "theme_list_page_click";
    //Edit Post click点击时间
    public static final String EVENT_POST_EDIT_VIDEO_CLICK = "post_edit_video_click"; //保留
    //首页banner item line impression
    @Deprecated
    public static final String EVENT_HOME_ADS_IMPRESSION = "home_ads_impression";
    //首页rtg impression埋点
    @Deprecated
    public static final String EVENT_HOME_RTG_IMPRESSION = "home_rtg_impression";
    //告诉我们你想要什么
    @Deprecated
    public static final String EVENT_EMPTY_SEARCH_PROMPT_CLICK = "empty_search_prompt_click";
    @Deprecated
    public static final String EVENT_CHECKOUT_PAGE_VIEW = "checkout_page_view";
    @Deprecated
    public static final String EVENT_CHECKOUT_CONFIRM_CLICK = "checkout_confirm_click";
    //消息中心点击
    @Deprecated
    public static final String EVENT_NOTIFICATION_SECTION_CLICK = "notification_section_click";
    //社区消息点击
    @Deprecated
    public static final String EVENT_NOTIFICATION_COMMUNITY_CLICK = "notification_community_click";
    //category点击: 用户进入category page后点击2级分类时上报
    @Deprecated
    public static final String EVENT_SHOPPING_LIST_CLICK = "shopping_list_click";
    // search
    @Deprecated
    public static final String EVENT_SEARCH_CLICK = "search_click";
    //
    @Deprecated
    public static final String EVENT_BRAND_VIEW_IMPRESSION = "brand_view_impression";
    //
    @Deprecated
    public static final String EVENT_BRAND_VIEW_CLICK = "brand_view_click";
    //
    @Deprecated
    public static final String EVENT_HOME_ITEM_LINE_VIEW = "home_itemline_view";
    //
    @Deprecated
    public static final String EVENT_HOME_ITEM_LINE_CLICK = "home_itemline_click";
    //thematic page impression
    @Deprecated
    public static final String EVENT_THEME_PAGE_IMPRESSION = "theme_page_impression";
    //
    @Deprecated
    public static final String EVENT_THEME_PAGE_CLICK = "theme_page_click";
    //
    @Deprecated
    public static final String EVENT_PRODUCT_DETAIL_SECTION_PRODUCT_IMPRESSION = "product_detail_section_product_impression";
    //购物车曾经购买
    @Deprecated
    public static final String EVENT_BOUGHT_BEFORE_CLICK = "bought_before_click";
    //商家页面列表曝光埋点
    @Deprecated
    public static final String EVENT_VENDOR_LIST_IMPRESSION = "vendor_list_impression";
    //商家页面列表点击埋点
    @Deprecated
    public static final String EVENT_VENDOR_LIST_CLICK = "app_vendor_out_click";
    //market place埋点
    //shopping_page_section_impression
    @Deprecated
    public static final String EVENT_SHOPPING_PAGE_SECTION_IMPRESSION = "shopping_page_section_impression";
    //app_categories_out_click
    @Deprecated
    public static final String EVENT_APP_CATEGORIES_OUT_CLICK = "app_categories_out_click";
    @Deprecated
    public static final String ACTIVITY_CENTER_VIEW = "activity_center_view";
    @Deprecated
    public static final String ACTIVITY_CENTER_CLICK = "activity_center_click";
    @Deprecated
    public static final String ACTIVITY_CENTER_IMPRESSION = "activity_center_impression";
    //    public static final String EVENT_VENDOR_LIST_CLICK = "app_vendor_out_click";
//    public static final String EVENT_VENDOR_LIST_CLICK = "app_vendor_out_click";
    // persistent time banner impression
    @Deprecated
    public static final String EVENT_TIME_BANNER_IMPRESSION = "timer_banner_impression";
    // persistent time banner click
    @Deprecated
    public static final String EVENT_TIME_BANNER_CLICK = "timer_banner_click";
    @Deprecated
    public static final String EVENT_ACTIVITY_CARD_CLICK = "activity_card_click";
    //Event name: cart_page_view
    @Deprecated
    public static final String EVENT_CART_PAGE_VIEW = "cart_page_view";
    //Event name: cart_page_click
    @Deprecated
    public static final String EVENT_CART_PAGE_CLICK = "cart_page_click";
    // 商品Category Tag 点击
    @Deprecated
    public static final String EVENT_PRODUCT_TAG_CLICK = "product_view_tag_click";
    //Event: onboarding_vip_trial_impression → VIP trial popup弹窗曝光
    @Deprecated
    public static final String EVENT_ONBOARDING_VIP_TRIAL_IMPRESSION = "onboarding_vip_trial_impression";
    //Event: onboarding_vip_trial_click → VIP trial popup点击
    @Deprecated
    public static final String EVENT_ONBOARDING_VIP_TRIAL_CLICK = "onboarding_vip_trial_click";
    //referrer click
    @Deprecated
    public static final String EVENT_INVITE_FRIEND_SIGNUP_POPUP_CLICK = "invite_friend_signup_popup_click";

    public interface HomeModuleType {
        String CM_TOP_MESSAGE = "cm_top_message";
        String CM_MAIN_BANNER = "cm_main_banner";
        String CM_NOTICE_BANNER = "cm_notice_banner";
        String CM_CATEGORIES = "cm_categories";
        String CM_CUISINES = "cm_cuisines";
        String CM_BUNDLES = "cm_bundles";
        String CM_FEATURE = "cm_feature";
        String CM_LIGHTNING_DEALS = "cm_lightning_deals";
        String CM_DEAL_OF_WEEK = "cm_deal_of_week";
        String CM_COLLECTION = "cm_collection";
        String CM_SHARE_BANNER = "cm_share_banner";
        String CM_RECOMMEND_PREFERENCE = "cm_recommend-preference";
        String CM_RECOMMEND_POST = "cm_recommend-post";
        String CM_RECOMMEND_BREAKFAST = "cm_recommend-breakfast";
        String CM_ITEM_LINE = "cm_item_line";
        String CM_THEME = "cm_theme";
        String CM_BANNER_WITH_TITLE = "cm_banner_with_title";
        String CM_BANNER_LINE = "cm_banner_line";
        String CM_RTG = "cm_rtg";
    }

    public interface EventKey {
        String DISCOUNT_TYPE = "discount_type";
        String DISCOUNT_DETAIL = "discount_detail";
    }

    public interface PageView {
        String PAGE_START = "page_start";
        String HOME = "home";
        String CATEGORY = "category";
        String ME = "me";
        String LOGIN_SIGN_UP = "login_signup";
        String SEARCH_RESULT = "search_result"; //主动触发
        String PRODUCT_REVIEW = "product_review";
        String CART = "cart";
        String CHECKOUT = "checkout";
        String COUPON = "coupon";
        String CHECKOUT_PRODUCT_LIST = "checkout_product_list";
        String PAYMENT_METHOD = "payment_method";
        String COMM_HOME = "comm_home"; //社区首页
        String ME_PROFILE = "me_profile";
        String COMM_PROFILE = "comm_profile"; //进入别人的profile
        String BOGO = "bogo";
        String ADDRESS = "address";
        String BEFORE_YOU_CHECKOUT = "before_you_checkout";
        String CART_SELECTION = "cart_selection";
        String COMM_FOLLOWING = "comm_following";
        String COMM_SEARCH_RESULT = "comm_search_result";
        String SELLER_SEARCH_LANDING = "mkpl_store_search_landing";
        String SELLER_SEARCH_RESULT = "mkpl_store_search_result";
        String GLOBAL_SEARCH_LANDING = "mkpl_global_search_landing";
        String GLOBAL_SEARCH_RESULT = "mkpl_global_search_result";
        String COMM_VIDEO = "comm_video";
        String COMM_VIDEO_EDIT = "comm_video_edit";
        String CUISINE = "cuisine"; //主动触发，区分cate和cuisine
        String DELIVERY_DATE_SELECTION = "delivery_date_selection";
        String ME_MESSAGE_CENTER = "me_message_center";
        String COMMUNITY_ACTIVITY = "community_activity";
        String ACTIVITY_CENTER = "activity_center";
        String NEW_ADDRESS = "new_address";
        String ONBOARDING = "onboarding";
        String ONBOARDING_STORE = "onboarding_store";   //主动触发
        String ONBOARDING_ZIP = "onboarding_zip";   //主动触发
        String ZIPCODE = "zipcode";
        String PAYMENT_NEW_CARD = "payment_new_card";
        String PRODUCT = "product";
        String NOTIFICATION_COMMUNITY = "_community";
        String NOTIFICATION_CENTER = "me_message_center";
        String PRODUCT_REVIEW_LIST = "product_review_list";
        String STORE_SELECTION = "store_selection"; //主动触发
        String SEARCH_LANDING = "search_landing"; //主动触发
        //String VENDOR = "vendor";
        String VENDOR = "mkpl_vendor";
        String ME_REVIEW_DETAILS_PAGE_VIEW = "me_review_details";//主动触发
        String PRODUCT_REVIEW_PAGE_VIEW = "product_review";//主动触发
        String INVITE_FRIEND_SIGNUP_POPUP = "invite_friend_signup_popup";//主动触发
        String SUGGEST_TRANSLATION = "suggest_translation";//主动触发
        String BRAND = "brand";
        String SEARCH_BY_IMAGE = "search_by_image";
        String SCAN = "scan";
        String THEME = "theme";
        String CHANGE_DELIVERY_OPTION = "change_delivery_option";
        String LOGIN_ACCOUNT_VERIFY = "login_account_verify";
        String LOGIN_STUDENT_VERIFY = "student_account_verify";
        String LOGIN_STUDENT_PREVERIFY = "student_account_preverify";
        String MKPL_VENDOR_LANDING = "mkpl_vendor_landing";
        String MKPL_SELLER = "mkpl_seller";
        String CHANGE_ADDRESS = "change_address";
        String MKPL_VENDOR_DETAIL = "mkpl_seller_detail";
        String POST_REVIEW = "post_review";
        String ME_REVIEW_EDIT = "me_review_edit";
        String POST_REVIEW_SUBMIT = "post_review_submit";
        String MKPL_EXPLORE_VIDEO = "comm_mkpl_seller_video";
        String MKPL_WATERFALL = "mkpl_waterfall";
        String MKPL_MINI_CART = "mkpl_minicart";
        String SET_PWD = "set_pwd";
        String ONBOARDING_SIGNUP = "onboarding_signup";
        String PRODUCT_FAVOR = "product_favor";
        String FBW_SELLER = "fbw_seller";
        String PROMOTION_RECOMMEND = "promotion_recommend";
        String TRADE_IN = "trade_in";
        String BOT_LANDING = "bot_landing";
        String BOT_RESULT = "bot_result";
        String CMS_ACTIVITY = "cms_activity";
        String AUTO_COLLECTION = "sayweee_collection";
        String MY_ORDER = "me_my_order";
        String MY_ORDER_SEARCH_LANDING = "my_order_search_landing";
        String EBT_CARD_ADD = "ebt_card_add";
        String AI_BOT_ASSISTANT_RESULT = "ai_bot_assistant_result";
        String CHECKOUT_DEAL_PAY = "checkout_dealpay";
    }
}
