package com.sayweee.weee.service.location;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.google.android.gms.maps.GoogleMap;
import com.google.android.gms.maps.GoogleMapOptions;
import com.google.android.gms.maps.MapView;
import com.google.android.gms.maps.OnMapReadyCallback;
import com.google.android.gms.maps.model.CameraPosition;
import com.google.android.gms.maps.model.LatLng;
import com.sayweee.wrapper.core.BaseViewModel;
import com.sayweee.wrapper.core.compat.ResponseTransformer;
import com.sayweee.wrapper.core.compat.SimpleObserver;
import com.sayweee.wrapper.core.model.BaseModel;

import java.util.List;

import io.reactivex.Observable;
import io.reactivex.ObservableEmitter;
import io.reactivex.ObservableOnSubscribe;

/**
 * Author:  winds
 * Date:    12/22/21.
 * Desc:
 */
public class MapDecoderViewModel<M extends BaseModel> extends BaseViewModel<M> {

    static final LatLng KYIV = new LatLng(50.450311, 30.523730);

    public MapDecoderViewModel(@NonNull Application application) {
        super(application);
    }

    protected void onMapStartDecode(List<LatLng> list) {
        GoogleMapOptions options = new GoogleMapOptions()
                .compassEnabled(false)
                .mapToolbarEnabled(false)
                .camera(CameraPosition.fromLatLngZoom(KYIV, 15))
                .liteMode(true);
        MapView mapView = new MapView(getApplication(), options);
        Observable.create(new ObservableOnSubscribe<GoogleMap>() {
            @Override
            public void subscribe(@io.reactivex.annotations.NonNull ObservableEmitter<GoogleMap> emitter) throws Exception {
                mapView.onCreate(null);
                mapView.onStart();
                mapView.onResume();
                mapView.getMapAsync(new OnMapReadyCallback() {
                    @Override
                    public void onMapReady(GoogleMap googleMap) {
                        emitter.onNext(googleMap);
                        emitter.onComplete();
                    }
                });
            }
        }).compose(ResponseTransformer.scheduler(this, true))
                .subscribe(new SimpleObserver<GoogleMap>() {
                    @Override
                    public void onNext(@Nullable GoogleMap map) {

                    }
                });

    }

    protected void onMapStartDecode(MapView mapView, List<LatLng> list) {
        GoogleMapOptions options = new GoogleMapOptions()
                .compassEnabled(false)
                .mapToolbarEnabled(false)
                .camera(CameraPosition.fromLatLngZoom(KYIV, 15))
                .liteMode(true);
        Observable.create(new ObservableOnSubscribe<GoogleMap>() {
            @Override
            public void subscribe(@io.reactivex.annotations.NonNull ObservableEmitter<GoogleMap> emitter) throws Exception {
                mapView.onCreate(null);
                mapView.onStart();
                mapView.onResume();
                mapView.getMapAsync(new OnMapReadyCallback() {
                    @Override
                    public void onMapReady(GoogleMap googleMap) {
                        emitter.onNext(googleMap);
                        emitter.onComplete();
                    }
                });
            }
        }).compose(ResponseTransformer.scheduler(this, true))
                .subscribe(new SimpleObserver<GoogleMap>() {
                    @Override
                    public void onNext(@Nullable GoogleMap map) {

                    }
                });

    }
}
