package com.sayweee.weee.service.pay;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleEventObserver;
import androidx.lifecycle.LifecycleOwner;

import java.util.ArrayList;
import java.util.List;

@SuppressWarnings("squid:S6548")
public final class PaymentCallbackHelper {

    private static class Builder {
        private static final PaymentCallbackHelper INSTANCE = new PaymentCallbackHelper();
    }

    private final List<PaymentHelper.OnPaymentCallback> callbacks = new ArrayList<>();

    private PaymentCallbackHelper() {

    }

    public static PaymentCallbackHelper get() {
        return Builder.INSTANCE;
    }

    public void onPaymentResult(boolean result, String url) {
        dispatchPaymentResult(result, url);
    }

    public void onPaymentRedirect(String url, String cancelUrl) {
        for (PaymentHelper.OnPaymentCallback callback : getCallbacks()) {
            if (callback instanceof PaymentHelper.OnPaymentRedirectCallback) {
                ((PaymentHelper.OnPaymentRedirectCallback) callback).onPaymentRedirect(url, cancelUrl);
            }
        }
    }

    private void dispatchPaymentResult(boolean result, String url) {
        for (PaymentHelper.OnPaymentCallback callback : getCallbacks()) {
            callback.onResult(result, url);
        }
    }

    @SuppressWarnings("unused")
    public void registerPaymentCallback(PaymentHelper.OnPaymentCallback callback) {
        registerPaymentCallback(null, callback);
    }

    public void registerPaymentCallback(@Nullable Object lifecycleOwner, PaymentHelper.OnPaymentCallback callback) {
        boolean isAdd = false;
        if (callback != null && !callbacks.contains(callback)) {
            callbacks.add(callback);
            isAdd = true;
        }
        if (isAdd && lifecycleOwner instanceof LifecycleOwner) {
            ((LifecycleOwner) lifecycleOwner).getLifecycle().addObserver(new LifecycleEventObserver() {
                @Override
                public void onStateChanged(@NonNull LifecycleOwner lifecycleOwner, @NonNull Lifecycle.Event event) {
                    if (event == Lifecycle.Event.ON_DESTROY) {
                        unregisterPaymentCallback(callback);
                        lifecycleOwner.getLifecycle().removeObserver(this);
                    }
                }
            });
        }
    }

    public void unregisterPaymentCallback(PaymentHelper.OnPaymentCallback callback) {
        if (callback != null) {
            callbacks.remove(callback);
        }
    }

    private List<PaymentHelper.OnPaymentCallback> getCallbacks() {
        // This method can be used to retrieve the current list of callbacks if needed
        return new ArrayList<>(callbacks);
    }
}
