package com.sayweee.weee.service.upload;

import androidx.annotation.NonNull;
import androidx.annotation.StringDef;

import java.io.File;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

public interface IUploadContent {

    String SUB_TYPE_IMAGE = "image";
    String SUB_TYPE_VIDEO = "video";
    String SUB_TYPE_THUMBNAIL = "thumbnail";
    String SUB_TYPE_DOC = "doc";

    @StringDef({SUB_TYPE_IMAGE, SUB_TYPE_VIDEO, SUB_TYPE_THUMBNAIL, SUB_TYPE_DOC})
    @Retention(RetentionPolicy.SOURCE)
    @interface SubType {

    }

    long getUploadId();

    @NonNull
    String getContentType();

    @SubType
    @NonNull
    String getSubType();

    int getUploadTotal();

    int getUploadProgress();

    void updateUploadProgress(int progress);

    void updateServerUrl(String serverUrl);

    @NonNull
    File getFile();

}
