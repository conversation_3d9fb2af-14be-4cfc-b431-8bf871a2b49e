package com.sayweee.weee.service.upload;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.sayweee.weee.global.config.AppConfig;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.service.upload.data.MixUploadRequest;
import com.sayweee.weee.service.upload.data.MixUploadResponse;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.JsonUtils;
import com.sayweee.weee.utils.function.Consumer;
import com.sayweee.wrapper.helper.lifecycle.LifecycleProvider;
import com.sayweee.wrapper.http.RetrofitIml;
import com.sayweee.wrapper.http.support.Utils;
import io.reactivex.Observable;
import io.reactivex.ObservableSource;
import io.reactivex.functions.Function;
import io.reactivex.schedulers.Schedulers;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import org.jetbrains.annotations.NotNull;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public final class MixUploader {

    public static Observable<MixUploadResponse> rxUpload(
            @NonNull String bizType,
            @NonNull List<IUploadContent> contents,
            @Nullable final Consumer<Void> onProgressUpdateListener
    ) {
        if (Utils.isNetworkConnected(LifecycleProvider.get().getApplication())) {
            return internalRxUpload(bizType, contents, onProgressUpdateListener);
        }
        return Observable.error(new IOException("MixUploader rxUpload network error"));
    }

    private static Observable<MixUploadResponse> internalRxUpload(
            @NonNull final String bizType,
            @NonNull final List<IUploadContent> contents,
            @Nullable final Consumer<Void> onProgressUpdateListener
    ) {
        MixUploadRequest request = buildRequest(bizType, contents);

        String url = getUploadUrl(request);

        MultipartBody.Builder builder = new MultipartBody.Builder();
        builder.setType(MultipartBody.FORM);
        for (IUploadContent content : contents) {
            ProgressListener progressListener;
            progressListener = new ProgressListener(content.getUploadId()) {

                @Override
                public void onProgressChanged(@NonNull ProgressInfo progressInfo) {
                    int progress = (int) (progressInfo.getPercent() * 100f);
                    content.updateUploadProgress(progress);
                    if (onProgressUpdateListener != null) {
                        onProgressUpdateListener.accept(null);
                    }
                }

                @Override
                public void onError(long id, Exception e) {

                }
            };

            RequestBody requestBody;
            requestBody = new ProgressRequestBody(
                    /* id= */content.getUploadId(),
                    /* file= */content.getFile(),
                    /* contentType= */content.getContentType(),
                    /* listener= */progressListener
            );
            builder.addFormDataPart(
                    "file",
                    content.getFile().getName(),
                    requestBody
            );
        }

        Observable<MixUploadResponse> observable;
        observable = getLoader().mixUpload(
                url,
                AccountManager.get().getBearerToken(),
                builder.build().parts()
        ).map(response -> {
            if (response == null) {
                throw new NullPointerException("Null mix upload response");
            }
            if (!EmptyUtils.isEmpty(response.getData())) {
                for (int i = 0; i < response.getData().size(); i++) {
                    MixUploadResponse.UploadResult result = response.getData().get(i);
                    IUploadContent content = CollectionUtils.getOrNull(contents, i);
                    if (content != null) {
                        content.updateServerUrl(result.url);
                    }
                }
            }
            return response;
        });
        return observable;
    }

    public static Observable<MixUploadResponse> rxUploadQueued(
            @NonNull String bizType,
            @NonNull List<IUploadContent> contents,
            @Nullable final Consumer<Void> onProgressUpdateListener
    ) {
        if (Utils.isNetworkConnected(LifecycleProvider.get().getApplication())) {
            return internalRxUploadQueued(bizType, contents, onProgressUpdateListener);
        }
        return Observable.error(new IOException("MixUploader rxUpload network error"));
    }

    private static Observable<MixUploadResponse> internalRxUploadQueued(
            @NonNull final String bizType,
            @NonNull final List<IUploadContent> contents,
            @Nullable final Consumer<Void> onProgressUpdateListener
    ) {
        Observable<MixUploadResponse> observable;
        observable = Observable.fromIterable(contents)
                .flatMap((Function<IUploadContent, ObservableSource<MixUploadResponse>>) content -> {
                    MixUploadRequest request = buildRequest(bizType, CollectionUtils.arrayListOf(content));
                    String url = getUploadUrl(request);
                    MultipartBody.Builder builder = new MultipartBody.Builder();
                    builder.setType(MultipartBody.FORM);

                    ProgressListener progressListener;
                    progressListener = new ProgressListener(content.getUploadId()) {

                        @Override
                        public void onProgressChanged(@NonNull ProgressInfo progressInfo) {
                            int progress = (int) (progressInfo.getPercent() * 100f);
                            content.updateUploadProgress(progress);
                            if (onProgressUpdateListener != null) {
                                onProgressUpdateListener.accept(null);
                            }
                        }

                        @Override
                        public void onError(long id, Exception e) {

                        }
                    };

                    RequestBody requestBody;
                    requestBody = new ProgressRequestBody(
                            /* id= */content.getUploadId(),
                            /* file= */content.getFile(),
                            /* contentType= */content.getContentType(),
                            /* listener= */progressListener
                    );
                    builder.addFormDataPart(
                            "file",
                            content.getFile().getName(),
                            requestBody
                    );

                    return getLoader().mixUpload(
                                    url,
                                    AccountManager.get().getBearerToken(),
                                    builder.build().parts()
                            )
                            .subscribeOn(Schedulers.io())
                            .map(response -> {
                                MixUploadResponse.UploadResult result = CollectionUtils.firstOrNull(response.getData());
                                if (result != null) {
                                    content.updateServerUrl(result.url);
                                }
                                return response;
                            });
                })
                .toList()
                .map(new Function<List<MixUploadResponse>, MixUploadResponse>() {
                    @Override
                    public MixUploadResponse apply(@NotNull List<MixUploadResponse> mixUploadResponses) throws Exception {
                        return new MixUploadResponse();
                    }
                })
                .toObservable();
        return observable;
    }

    private static MixUploadRequest buildRequest(
            @NonNull String bizType,
            @NonNull List<IUploadContent> contents
    ) {
        List<MixUploadRequest.Item> items = new ArrayList<>(contents.size());
        for (IUploadContent content : contents) {
            List<String> files = new ArrayList<>(1);
            files.add(content.getFile().getName());
            items.add(new MixUploadRequest.Item(content.getSubType(), files));
        }
        return new MixUploadRequest(bizType, items);
    }

    @NonNull
    private static String getUploadUrl(@NonNull MixUploadRequest request) {
        String json = JsonUtils.toJSONString(request);
        Map<String, String> map = new HashMap<>();
        String key = "requestData";
        String value;
        try {
            value = URLEncoder.encode(json, "UTF-8");
        } catch (Exception ignored) {
            value = "";
        }
        map.put(key, value);
        return CommonTools.packetUrlParams(AppConfig.HOST_UPLOAD + AppConfig.API_UPLOAD_MIX, map);
    }

    @NonNull
    private static ResourceApi getLoader() {
        return RetrofitIml.get().getHttpService(ResourceApi.class);
//        if (RetrofitUploadImpl.get().getRetrofitClient() == null) {
//            RetrofitUploadImpl.get().initHttp(RetrofitIml.get().getRetrofitClient());
//        }
//        return RetrofitUploadImpl.get().getHttpService(ResourceApi.class);
    }

    private MixUploader() {

    }

}
