package com.sayweee.weee.service.helper;

import androidx.annotation.NonNull;

import com.sayweee.monitor.Config;
import com.sayweee.monitor.IMonitorParams;
import com.sayweee.monitor.WeeeMonitor;
import com.sayweee.service.ConfigService;
import com.sayweee.service.ContextService;
import com.sayweee.weee.global.config.DevConfig;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.service.config.ConfigManager;
import com.sayweee.weee.service.config.bean.MonitorConfigBean;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.DateUtils;
import com.sayweee.weee.utils.DecimalTools;
import com.sayweee.wrapper.helper.lifecycle.LifecycleProvider;

public final class MonitorHelper {

    private MonitorHelper() {

    }

    public static void init() {
        WeeeMonitor.getInstance().production(DevConfig.isProd());
        WeeeMonitor.getInstance().injectConfig(new MonitorParams());
        WeeeMonitor.getInstance().setInterceptor(new WeeeMonitor.Interceptor() {
            @Override
            public boolean intercept(@NonNull String eventType, @NonNull String pageKey, long duration) {
                if (WeeeMonitor.APP_INIT.equals(eventType)) {
                    if (WeeeMonitor.KEY_START.equals(pageKey)) {
                        return duration >= DateUtils.UNIT_SEC * 45;
                    } else if (WeeeMonitor.KEY_ACTIVE.equals(pageKey)) {
                        return duration >= DateUtils.UNIT_SEC * 120;
                    }
                }
                return false;
            }
        });

    }

    public static void applyConfig() {
        Object config = ConfigService.get().getDynamicConfig(ConfigManager.DynamicConfig.MONITOR);
        if (config instanceof MonitorConfigBean) {
            MonitorConfigBean target = (MonitorConfigBean) config;
            int compareVersion = CommonTools.compareAppVersion(DevConfig.VERSION_NAME, target.android_version);
            boolean enable = (compareVersion >= 0) && (getCoefficient() <= target.coefficient);
            if (enable) {
                WeeeMonitor.getInstance().enable(true);
            } else {
                WeeeMonitor.getInstance().enable(false);
                WeeeMonitor.getInstance().enableCache(false);
                WeeeMonitor.getInstance().clear();
            }
        }
    }

    /**
     * 获取随机执行比例的值
     *
     * @return coefficient
     */
    @SuppressWarnings("squid:S2245")
    private static float getCoefficient() {
        return DecimalTools.parseFloat(DecimalTools.format(DecimalTools.FORMAT_NORMAL, Math.random()));
    }

    static class DefaultMonitorParams implements IMonitorParams {

        @Override
        public String getProjectId() {
            return Config.PROJECT_ID_DEFAULT;
        }

        @Override
        public String getD1() {
            return ContextService.get().getDeviceId(LifecycleProvider.get().getApplication());
        }

        @Override
        public String getD2() {
            return ContextService.get().getFingerprintData();
        }

        @Override
        public String getUserId() {
            return AccountManager.get().getUserId();
        }

        @Override
        public String getVersionName() {
            return DevConfig.VERSION_NAME;
        }

    }
}
