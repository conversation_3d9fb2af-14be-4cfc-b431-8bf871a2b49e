package com.sayweee.weee.service.timer;

import android.content.Context;
import android.content.SharedPreferences;
import android.view.View;
import android.widget.ImageView;

import androidx.annotation.Nullable;

import com.sayweee.weee.R;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.global.manager.ImageLoader;
import com.sayweee.weee.module.cart.bean.ReminderBean;
import com.sayweee.weee.module.cart.service.OrderApi;
import com.sayweee.weee.module.web.WebViewActivity;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackModel;
import com.sayweee.weee.service.rx.DisposableTransformer;
import com.sayweee.weee.service.track.TrackParams;
import com.sayweee.weee.service.webp.ImageSpec;
import com.sayweee.weee.service.webp.WebpManager;
import com.sayweee.weee.utils.DateUtils;
import com.sayweee.weee.utils.DecimalTools;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.widget.TimerTextView;
import com.sayweee.weee.widget.timer.OnSimpleTimerListener;
import com.sayweee.wrapper.bean.ResponseBean;
import com.sayweee.wrapper.core.compat.ResponseObserver;
import com.sayweee.wrapper.http.RetrofitIml;
import com.sayweee.wrapper.utils.PreferenceUtils;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * Author:  ycy
 */
public class ReminderBannerManager {
    private static final String UNPAID_REMINDER = "unpaid_reminder";
    private static final String KEY_BANNER_DISPLAYED_INFO = "key_banner_displayed_info";
    private static final ReminderBannerManager instance = new ReminderBannerManager();

    public static ReminderBannerManager get() {
        return instance;
    }

    private ReminderBannerManager() {

    }

    List<BannerChangedListener> listeners = new ArrayList<>();

    private OrderApi getOrderLoader() {
        return RetrofitIml.get().getHttpService(OrderApi.class);
    }

    public interface BannerChangedListener {
        void onBannerChange(@Nullable ReminderBean reminderBean);

        void onBannerDismiss();
    }

    public void register(BannerChangedListener listener) {
        if (listener != null && listeners != null && !listeners.contains(listener)) {
            listeners.add(listener);
        }
    }

    public void unregister(BannerChangedListener listener) {
        if (listeners != null && listener != null) {
            listeners.remove(listener);
        }
    }

    public void refresh() {
        if (!AccountManager.get().isLogin()) {
            return;
        }
        String regex = "-";
        String s = getPreferences().getString(KEY_BANNER_DISPLAYED_INFO, null);
        if (s != null) {
            String[] split = s.split(regex);
            if (split.length == 2) {
                String userId = split[0];
                long timeMillis = DecimalTools.parseLong(split[1]);
                if (AccountManager.get().getUserId().equals(userId) && DateUtils.isSameDay(timeMillis, System.currentTimeMillis())) {
                    //Skip refresh: same user and day
                    return;
                }
            }
        }
        getOrderLoader()
                .getUnpaidReminder()
                .compose(DisposableTransformer.scheduler(true))
                .subscribe(new ResponseObserver<ResponseBean<ReminderBean>>() {
                    @Override
                    public void onResponse(ResponseBean<ReminderBean> response) {
                        if (response.getData() == null) {
                            return;
                        }
                        onBannerChange(response.getData());
                        long timeMillis = System.currentTimeMillis();
                        String userId = AccountManager.get().getUserId();
                        getPreferences().edit().putString(KEY_BANNER_DISPLAYED_INFO, userId + regex + timeMillis).apply();
                    }
                });
    }

    private void onBannerChange(ReminderBean reminderBean) {
        if (listeners != null) {
            for (BannerChangedListener listener : listeners) {
                listener.onBannerChange(reminderBean);
            }
        }
    }

    public void dismissBanner() {
        if (listeners == null) {
            return;
        }
        Iterator<BannerChangedListener> iterator = listeners.iterator();
        while (iterator.hasNext()) {
            BannerChangedListener listener = iterator.next();
            listener.onBannerDismiss();
            iterator.remove();
        }
    }

    private final String targetNm = "complete_payment";
    public void setBannerInfo(View layoutUnpaidReminder, ReminderBean reminderBean) {
        if (reminderBean == null) {
            ViewTools.setViewVisibilityIfChanged(layoutUnpaidReminder, false);
            return;
        }
        AppAnalytics.logEvent(EagleTrackEvent.EventType.POPUP_IMP, new EagleTrackModel.Builder()
                .addContent(new TrackParams()
                        .put("action", "view")
                        .put("name", targetNm)
                        .put("target_url", reminderBean.link_url)
                        .get()).build().getParams());
        layoutUnpaidReminder.postDelayed(() -> {
            ViewTools.setViewVisibilityIfChanged(layoutUnpaidReminder, true);
        }, 300); // 延迟300毫秒
//        ViewTools.setViewVisibilityIfChanged(layoutUnpaidReminder, true);
        TimerTextView tvTimer = layoutUnpaidReminder.findViewById(R.id.tv_timer);
        ImageView ivIcon = layoutUnpaidReminder.findViewById(R.id.iv_icon);
        if (tvTimer == null || ivIcon == null) {
            return;
        }
        tvTimer.setCountDownFormat(" %1$02d : %2$02d : %3$02d ");
        long endTime = reminderBean.end_time - reminderBean.current_time + (int) (System.currentTimeMillis() / 1000);
        tvTimer.start(endTime);
        tvTimer.setOnTimerListener(new OnSimpleTimerListener() {
            @Override
            public void onEnd() {
                ViewTools.setViewVisibilityIfChanged(layoutUnpaidReminder, false);
            }
        });
        Context context = layoutUnpaidReminder.getContext();
        ViewTools.setViewOnClickListener(layoutUnpaidReminder, v -> {
            AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                    .setTargetNm(targetNm)
                    .setTargetPos(-1)
                    .setTargetType(EagleTrackEvent.TargetType.POPUP)
                    .setClickType(EagleTrackEvent.ClickType.VIEW)
                    .build().getParams());
            context.startActivity(WebViewActivity.getIntent(context, reminderBean.link_url));
            ViewTools.setViewVisibilityIfChanged(layoutUnpaidReminder, false);
        });
        ImageLoader.load(context,
                ivIcon,
                WebpManager.get().getConvertUrl(ImageSpec.SPEC_64, reminderBean.product_image_url),
                R.mipmap.iv_product_placeholder);
    }

    public SharedPreferences getPreferences() {
        return PreferenceUtils.getSharedPreferences(UNPAID_REMINDER);
    }
}