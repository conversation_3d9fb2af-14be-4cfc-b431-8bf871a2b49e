package com.sayweee.weee.service.config;

import com.sayweee.weee.module.launch.bean.ConfigListBean;
import com.sayweee.weee.service.config.bean.ConfigContentBean;
import com.sayweee.wrapper.bean.ResponseBean;
import com.sayweee.wrapper.bean.SimpleResponseBean;

import java.io.Serializable;
import java.util.Map;

import io.reactivex.Observable;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.PUT;
import retrofit2.http.Query;

/**
 * Author:  winds
 * Date:    2022/7/22.
 * Desc:
 */
public interface ConfigApi {

    @GET("/ec/content/config/v2/all")
    Observable<ResponseBean<ConfigContentBean>> getConfig();

    @GET("/ec/content/config/getConfig")
    Observable<ResponseBean<ConfigListBean>> getConfig(@Query("configKeyList") String config);

    @GET("/ec/content/config/getConfig")
    Observable<ResponseBean<Map<String, String>>> getConfigMap(@Query("configKeyList") String config);

    @PUT("/central/content/config/update")
    Observable<SimpleResponseBean> updateConfig(@Body Map<String, Serializable> params);

    @POST("/central/content/config/add")
    Observable<SimpleResponseBean> addConfig(@Body Map<String, Serializable> params);

}
