package com.sayweee.weee.service.track;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;

import com.alibaba.fastjson.JSONObject;
import com.sayweee.logger.Logger;
import com.sayweee.logger.Utils;
import com.sayweee.track.callback.InterceptorCallback;
import com.sayweee.track.convert.IConverter;
import com.sayweee.track.core.IPlatform;
import com.sayweee.track.interceptor.Interceptor;
import com.sayweee.track.model.Target;
import com.sayweee.track.platform.weee.WeeePlatform;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.module.launch.service.StoreManager;
import com.sayweee.weee.utils.JsonUtils;
import com.sayweee.wrapper.bean.FailureBean;
import com.sayweee.wrapper.bean.ResponseBean;
import com.sayweee.wrapper.core.compat.ResponseObserver;
import com.sayweee.wrapper.core.compat.ResponseTransformer;
import com.sayweee.wrapper.helper.lifecycle.LifecycleProvider;
import com.sayweee.wrapper.http.RetrofitIml;

import java.math.BigInteger;
import java.security.MessageDigest;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Vector;

import okhttp3.RequestBody;


/**
 * Author:  winds
 * Email:   <EMAIL>
 * Date:    2020/7/27.
 * Desc:
 */
public class WeeeInterceptor implements Interceptor {
    private final int length = 20; //一次完整的上报条数
    public String lastTarget;
    private final List<JSONObject> list = new Vector<>();
    private final List<String> uploadEvents = new Vector<>();

    private final List<JSONObject> extendList = new Vector<>();
    private final List<String> uploadExtendsEvents = new Vector<>();

    @Override
    public void process(IPlatform platform, int type, String eventName, Map<String, Object> params, boolean convert, InterceptorCallback callback) {
        if (platform instanceof WeeePlatform) {
            String target = eventName;
            IConverter converter = platform.getConverter();
            if (converter != null && convert) {
                target = converter.convertEvent(eventName);
            }
            if (type == Target.TYPE_PV) {
//                execPvTrack((WeeePlatform) platform, target, lastTarget);
                lastTarget = target;
            } else {
                AppAnalytics.logEvent(eventName, params);
            }
        }
        callback.onContinue(eventName, params, convert);
    }

    @Override
    public int getPriority() {
        return 100;
    }

    private JSONObject createPvItem(String target, String lastTarget) {
        JSONObject item = new JSONObject();
        item.put("type", "pv");
        item.put("page", target);
        item.put("time", System.currentTimeMillis() / 1000);
        JSONObject child = new JSONObject();
        child.put("referer", lastTarget);
        String storeKey = StoreManager.get().getStoreKey();
        if (storeKey != null) {
            child.put("store", storeKey);
        }
        item.put("params", child);
        return item;
    }

    private JSONObject createItem(String target, Map<String, Object> params) {
        JSONObject item = new JSONObject();
        item.put("type", target);
        item.put("time", System.currentTimeMillis() / 1000);
        JSONObject child = new JSONObject();
        if (params != null && params.size() > 0) {
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                child.put(entry.getKey(), entry.getValue());
            }
        }
        String storeKey = StoreManager.get().getStoreKey();
        if (storeKey != null) {
            child.put("store", storeKey);
        }
        item.put("params", child);
        return item;
    }

    private void execPvTrack(WeeePlatform platform, String target, String lastTarget) {
        execTrack(platform, createPvItem(target, lastTarget));
    }

    private void execTrack(WeeePlatform platform, JSONObject event) {
        list.add(event);
        if (list.size() >= length) {
            String session = AccountManager.get().getSessionToken();
            long current = System.currentTimeMillis();
            String time = String.valueOf(current);
            String message_id = getMD5String(session + time);
            JSONObject obj = new JSONObject();
            obj.put("message_id", message_id);
            obj.put("event_items", list);
            String data = JsonUtils.toJSONString(obj);
            uploadEvents.add(data);
            list.clear();
        }
        tryUpload();
    }

    private void execTrackExtend(WeeePlatform platform, JSONObject event) {
        extendList.add(event);
        if (extendList.size() >= length) {
            String session = AccountManager.get().getSessionToken();
            long current = System.currentTimeMillis();
            String time = String.valueOf(current);
            String message_id = getMD5String(session + time);
            JSONObject obj = new JSONObject();
            obj.put("message_id", message_id);
            obj.put("event_items", extendList);
            String data = JsonUtils.toJSONString(obj);
            uploadExtendsEvents.add(data);
            extendList.clear();
        }
        tryUpload();
    }

    private void tryUpload() {
        if (uploadEvents.size() > 0 && isNetworkConnected()) {
            try {
                Iterator<String> iterator = uploadEvents.iterator();
                while (iterator.hasNext()) {
                    String data = iterator.next();
                    iterator.remove();
                    WeeePlatform.get().log(data);
                    uploadTrack(data);
                }
            } catch (Exception e) {
                WeeePlatform.get().log("error", Utils.getStackTraceString(e));
            }
        }

        if (uploadExtendsEvents.size() > 0 && isNetworkConnected()) {
            try {
                Iterator<String> iterator = uploadExtendsEvents.iterator();
                while (iterator.hasNext()) {
                    String data = iterator.next();
                    iterator.remove();
                    WeeePlatform.get().log(data);
                    uploadTrackExtend(data);
                }
            } catch (Exception e) {
                WeeePlatform.get().log("error", Utils.getStackTraceString(e));
            }
        }
    }

    /**
     * 上传所有的日志
     * 在页面处于后台的时候上传
     */
    public void uploadAll() {
        if (list.size() > 0) {
            String session = AccountManager.get().getSessionToken();
            long current = System.currentTimeMillis();
            String time = String.valueOf(current);
            String message_id = getMD5String(session + time);

            JSONObject obj = new JSONObject();
            obj.put("message_id", message_id);
            obj.put("event_items", list);

            String data = JsonUtils.toJSONString(obj);
            uploadEvents.add(data);
            list.clear();
        }

        if (extendList.size() > 0) {
            String session = AccountManager.get().getSessionToken();
            long current = System.currentTimeMillis();
            String time = String.valueOf(current);
            String message_id = getMD5String(session + time);

            JSONObject obj = new JSONObject();
            obj.put("message_id", message_id);
            obj.put("event_items", extendList);

            String data = JsonUtils.toJSONString(obj);
            uploadExtendsEvents.add(data);
            extendList.clear();
        }
        tryUpload();
    }

    private void uploadTrack(final String data) {
        String weee_token = AccountManager.get().getToken();
        RequestBody body = RequestBody.create(okhttp3.MediaType.parse("application/json; charset=utf-8"), data);

        RetrofitIml.get().getHttpService(TrackApi.class)
                .trackEvent(weee_token, body)
                .compose(ResponseTransformer.scheduler())
                .subscribe(new ResponseObserver<ResponseBean>(false) {
                    @Override
                    public void onResponse(ResponseBean response) {

                    }

                    @Override
                    public void onError(FailureBean failure) {
                        uploadEvents.add(data);
                        WeeePlatform.get().log("failure", data);
                        Logger.error(failure.getException());
                    }
                });
    }

    private void uploadTrackExtend(final String data) {
        RequestBody body = RequestBody.create(okhttp3.MediaType.parse("application/json; charset=utf-8"), data);
        RetrofitIml.get().getHttpService(TrackApi.class)
                .trackEventNew(2, body)
                .compose(ResponseTransformer.scheduler())
                .subscribe(new ResponseObserver<ResponseBean>(false) {
                    @Override
                    public void onResponse(ResponseBean response) {

                    }

                    @Override
                    public void onError(FailureBean failure) {
                        uploadExtendsEvents.add(data);
                        WeeePlatform.get().log("failure", data);
                        Logger.error(failure.getException());
                    }
                });
    }

    public boolean isNetworkConnected() {
        try {
            ConnectivityManager mConnectivityManager = (ConnectivityManager) LifecycleProvider.get().getApplication().getSystemService(Context.CONNECTIVITY_SERVICE);
            NetworkInfo mNetworkInfo = mConnectivityManager.getActiveNetworkInfo();
            return mNetworkInfo != null && mNetworkInfo.isConnected();
        } catch (Exception e) {
            return true;
        }
    }

    @SuppressWarnings("squid:S4790")
    public String getMD5String(String str) {
        try {
            // 生成一个MD5加密计算摘要
            MessageDigest md = MessageDigest.getInstance("MD5");
            // 计算md5函数
            md.update(str.getBytes());
            // digest()最后确定返回md5 hash值，返回值为8位字符串。因为md5 hash值是16位的hex值，实际上就是8位的字符
            // BigInteger函数则将8位的字符串转换成16位hex值，用字符串来表示；得到字符串形式的hash值
            //一个byte是八位二进制，也就是2位十六进制字符（2的8次方等于16的2次方）
            return new BigInteger(1, md.digest()).toString(16);
        } catch (Exception e) {
            return "";
        }
    }
}
