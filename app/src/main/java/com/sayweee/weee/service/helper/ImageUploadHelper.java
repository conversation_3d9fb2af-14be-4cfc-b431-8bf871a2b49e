package com.sayweee.weee.service.helper;

import com.sayweee.weee.global.config.AppConfig;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.module.cart.service.OrderApi;
import com.sayweee.weee.module.search.bean.UploadResponseBean;
import com.sayweee.wrapper.http.RetrofitIml;

import java.io.File;

import io.reactivex.Observable;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;

/**
 * Author:  winds
 * Date:    2022/10/10.
 * Desc:
 */
public class ImageUploadHelper {

    public static Observable<UploadResponseBean> upload(String bizType, String subType, String filePath) {
        File file = new File(filePath);
        String url = String.format(AppConfig.HOST_UPLOAD + "/resource/v2/upload/directly?bizType=%1$s&subType=%2$s", bizType, subType);
        MultipartBody.Builder builder = new MultipartBody.Builder().setType(MultipartBody.FORM);
        RequestBody fileBody = RequestBody.create(null, file);
        builder.addFormDataPart("file", file.getName(), fileBody);
        return RetrofitIml.get().getHttpService(OrderApi.class)
                .uploadResource(url, AccountManager.get().getBearerToken(), builder.build().parts());
    }
}
