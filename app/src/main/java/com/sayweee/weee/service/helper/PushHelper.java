package com.sayweee.weee.service.helper;

import android.app.Application;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import androidx.collection.ArrayMap;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.android.gms.tasks.OnSuccessListener;
import com.google.android.gms.tasks.Task;
import com.google.firebase.messaging.FirebaseMessaging;
import com.klaviyo.analytics.Klaviyo;
import com.klaviyo.analytics.model.Profile;
import com.sayweee.logger.Logger;
import com.sayweee.scheduler.TaskScheduler;
import com.sayweee.weee.global.config.DevConfig;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.module.account.bean.AccountEmailBean;
import com.sayweee.weee.module.account.service.AccountApi;
import com.sayweee.weee.module.launch.service.LaunchApi;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.JsonUtils;
import com.sayweee.wrapper.bean.ResponseBean;
import com.sayweee.wrapper.bean.SimpleResponseBean;
import com.sayweee.wrapper.core.compat.ResponseObserver;
import com.sayweee.wrapper.core.compat.ResponseTransformer;
import com.sayweee.wrapper.core.compat.SimpleObserver;
import com.sayweee.wrapper.http.RetrofitIml;
import com.sayweee.wrapper.http.support.RequestParams;
import com.urbanairship.UAirship;

import java.io.Serializable;
import java.util.Map;
import java.util.Set;

import io.reactivex.schedulers.Schedulers;

public class PushHelper {

    private static final String KLAVIYO_FLAG = "com.klaviyo._k";

    public static void initKlaviyo(Application application, String apiKey) {
        Klaviyo.INSTANCE.initialize(apiKey, application);
        Task<String> token = FirebaseMessaging.getInstance().getToken();
        if (token != null) {
            token.addOnSuccessListener(new OnSuccessListener<String>() {
                @Override
                public void onSuccess(String s) {
                    safeSetKlaviyoPushToken(s);
                }
            });
        }
    }

    public static void onAccountChanged(boolean login) {
        if (login) {
            updateAccountInfo();
        } else {
            Klaviyo.INSTANCE.resetProfile();
        }
    }

    public static String getPushToken() {
        String pushToken = Klaviyo.INSTANCE.getPushToken();
        if (pushToken == null) {
            try {
                UAirship shared = UAirship.shared();
                pushToken = shared.getPushManager().getPushToken();
            } catch (Exception ignored) {/**/}
        }
        return pushToken;
    }

    public static String getChannelId() {
        String channelId = null;
        try {
            channelId = UAirship.shared().getChannel().getId();
        } catch (Exception e) {/**/}
        if (TextUtils.isEmpty(channelId)) {
            channelId = AccountManager.get().getPushChannelId();
        }
        return channelId;
    }

    public static void safeSetKlaviyoPushToken(String pushToken) {
        TaskScheduler.runOnUiThread(new Runnable() {
            @Override
            public void run() { //发现收到回调后，Klaviyo未初始化成功的情况
                Klaviyo.INSTANCE.setPushToken(pushToken);
                Logger.enable(DevConfig.isDebug()).json("==> token", pushToken);
            }
        }, 50);
    }

    public static boolean interceptKlaviyoIntent(Intent intent) {
        if (intent != null && Klaviyo.INSTANCE.isKlaviyoIntent(intent)) {
//            logKlaviyoEvent(intent.getExtras());
            Klaviyo.INSTANCE.handlePush(intent);
            return true;
        }
        return false;
    }

    public static String processUrlParams(Intent intent, String url) {
        if (intent != null) {
            Map<String, String> params = CommonTools.parseQueryParams(url);
            String extra = intent.getStringExtra(KLAVIYO_FLAG);
            JSONObject object = JSON.parseObject(extra);
            ArrayMap<String, String> appendParams = new ArrayMap<>();
            if (object != null) {
                String name = object.getString("Message Name");
                if (name != null && name.length() > 0) {
                    appendUrlParams(params, appendParams, "utm_campaign", name);
                    appendUrlParams(params, appendParams, "utm_content", name);
                }
            }
            appendUrlParams(params, appendParams, "utm_source", "push");
            appendUrlParams(params, appendParams, "utm_medium", "push_notification");
            appendUrlParams(params, appendParams, "utm_term", "push");
            String userId = AccountManager.get().getUserId();
            if (userId != null && userId.length() > 0) {
                appendUrlParams(params, appendParams, "uemail", userId);
            }
            return CommonTools.packetUrlParams(url, appendParams);
        }
        return url;
    }

    private static void appendUrlParams(Map<String, String> params, Map<String, String> appendParams, String key, String value) {
        if (!params.containsKey(key)) {
            appendParams.put(key, value);
        }
    }

    protected static void logKlaviyoEvent(Bundle bundle) {
        if (bundle != null && bundle.keySet() != null) {
            Map<String, Serializable> params = new ArrayMap<>();
            Set<String> keySet = bundle.keySet();
            for (String key : keySet) {
                if (key.contains("com.klaviyo")) {
                    String eventKey = key.replace("com.klaviyo.", "");
                    String eventValue = bundle.getString(key, "");
                    params.put(eventKey, eventValue);
                }
            }
            RetrofitIml.get().getHttpService(LaunchApi.class)
                    .logKlaviyoEvent(new RequestParams().putNonNull("push_token", PushHelper.getPushToken())
                            .putNonNull("param_json_data", JsonUtils.toJSONString(params))
                            .get())
                    .observeOn(Schedulers.io())
                    .subscribeOn(Schedulers.io())
                    .unsubscribeOn(Schedulers.io())
                    .subscribe(new SimpleObserver<SimpleResponseBean>() {
                        @Override
                        public void onNext(SimpleResponseBean responseBean) {
                            super.onNext(responseBean);
                        }
                    });
        }
    }

    public static void updateAccountInfoIfNeed() {
        if (AccountManager.get().isLogin()) {
            updateAccountInfo();
        }
    }

    private static void updateAccountInfo() {
        RetrofitIml.get()
                .getHttpService(AccountApi.class)
                .getAccountEmail()
                .compose(ResponseTransformer.scheduler())
                .subscribe(new ResponseObserver<ResponseBean<AccountEmailBean>>() {
                    @Override
                    public void onResponse(ResponseBean<AccountEmailBean> response) {
                        AccountEmailBean bean = response.getData();
                        if (bean.decrypt_email != null && !bean.decrypt_email.trim().isEmpty()) {
                            Klaviyo.INSTANCE.setEmail(bean.decrypt_email);
                        } else {
                            Klaviyo.INSTANCE.resetProfile();
                        }
                    }
                });
    }
}
