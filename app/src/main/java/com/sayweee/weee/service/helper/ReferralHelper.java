package com.sayweee.weee.service.helper;

import com.sayweee.scheduler.TaskScheduler;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.module.cart.service.OrderApi;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.wrapper.bean.SimpleResponseBean;
import com.sayweee.wrapper.core.compat.ResponseTransformer;
import com.sayweee.wrapper.core.compat.SimpleObserver;
import com.sayweee.wrapper.http.RetrofitIml;
import com.sayweee.wrapper.http.support.RequestParams;

import java.util.Map;

public class ReferralHelper {

    public static void onReceivedReferralUrl(String url) {
        TaskScheduler.execute(new Runnable() {
            @Override
            public void run() {
                if (AccountManager.get().isLogin()) {
                    reportReferralInfo(url, System.currentTimeMillis());
                } else {
                    AccountManager.get().setReferralUrl(url);
                }
            }
        });
    }

    public static void onAccountLogin() {
        TaskScheduler.execute(new Runnable() {
            @Override
            public void run() {
                String url = AccountManager.get().getReferralUrl();
                long receiveDate = AccountManager.get().getReferralReceiveDate();
                if (!EmptyUtils.isEmpty(url)) {
                    AccountManager.get().setReferralUrl(null);
                    reportReferralInfo(url, receiveDate);
                }
            }
        });
    }

    private static void reportReferralInfo(String url, long receivedDate) {
        if (receivedDate > 0 && url != null && url.trim().length() > 0) {
            long l = (System.currentTimeMillis() - receivedDate) / 1000;
            OrderApi api = RetrofitIml.get().getHttpService(OrderApi.class);
            if (api != null) {
                Map<String, String> params = CommonTools.parseQueryParams(url);
                String referralId = params.get("referral_id");
                api.reportReferralInfo(new RequestParams()
                                .put("referral_id", referralId)
                                .put("page_url", url)
                                .put("event_time_diff", l).get())
                        .compose(ResponseTransformer.scheduler(null, false))
                        .subscribe(new SimpleObserver<SimpleResponseBean>() {
                            @Override
                            public void onNext(SimpleResponseBean response) {
                                super.onNext(response);
                            }
                        });

            }
        }
    }
}
