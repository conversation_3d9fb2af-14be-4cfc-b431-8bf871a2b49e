package com.sayweee.weee.service.timer;

import android.view.View;

import com.sayweee.scheduler.TaskScheduler;
import com.sayweee.weee.module.cart.service.OrderApi;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackModel;
import com.sayweee.weee.service.rx.DisposableTransformer;
import com.sayweee.weee.service.timer.bean.TimerBannerBean;
import com.sayweee.weee.service.timer.service.TimerChangedListener;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.widget.TimerBannerView;
import com.sayweee.wrapper.bean.ResponseBean;
import com.sayweee.wrapper.bean.SimpleResponseBean;
import com.sayweee.wrapper.core.compat.ResponseObserver;
import com.sayweee.wrapper.core.compat.SimpleObserver;
import com.sayweee.wrapper.http.RetrofitIml;
import com.sayweee.wrapper.http.support.RequestParams;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

import io.reactivex.Observable;
import io.reactivex.annotations.NonNull;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Consumer;
import io.reactivex.functions.Function;

/**
 * Author:  winds
 * Date:    2/10/22.
 * Desc:
 * 1. 显示倒计时的timer 带背景
 * 2. 显示倒计时的timer 不带背景
 * 3. 不显示倒计时，自动关闭的timer
 * TYPE_IDLE. 不显示倒计时，不自动关闭的timer
 */
public class TimerBannerManager {

    public static int TYPE_IDLE = 100;

    private long intervalTime;
    private long countdown;

    private int h = 0;
    private int m = 0;
    private int s = 0;

    private TimerBannerBean targetData;
    private boolean dismissByUser;
    private Disposable timerDisposable;
    private final List<TimerChangedListener> listeners = new ArrayList<>();

    public static TimerBannerManager get() {
        return TimerBannerManager.Builder.instance;
    }

    private static final class Builder {
        private static final TimerBannerManager instance = new TimerBannerManager();
    }

    public TimerBannerManager() {
        dismissByUser = false;
    }

    /**
     * 刷新timer banner
     * new user flag、 session token changed 结算后
     *
     * @param reset
     */
    public void refreshTimerBanner(boolean reset) {
        if (reset) { //sessionToken 状态变化重置
            dismissByUser = false;
        }
        if (isDismissByUser()) { //用户主动取消
            return;
        }

        loadData();
    }

    public void prepareStartTimer() {
        long current = System.currentTimeMillis() / 1000;
        long deadlineTime = current + intervalTime;
        startTimer(deadlineTime - current);
    }

    public void prepareIdleTimer() {
        if (timerDisposable != null && !timerDisposable.isDisposed()) {
            timerDisposable.dispose();
        }
        if (listeners != null) {
            for (TimerChangedListener listener : listeners) {
                dispatch(listener, true, 0, 0, 0);
            }
        }
    }

    public void onAccountInfoChanged() {
        if (isDismissByUser()) {
            return;
        }
        refreshTimerBanner(false);
    }

    public void startTimer(long diff) {
        if (timerDisposable != null && !timerDisposable.isDisposed()) {
            timerDisposable.dispose();
        }
        long total = diff;
        Observable.interval(0, 1, TimeUnit.SECONDS)
                .take(total + 1)
                .map(new Function<Long, Long>() {
                    @Override
                    public Long apply(@NonNull Long l) throws Exception {
                        return total - l;
                    }
                })
                .doOnSubscribe(new Consumer<Disposable>() {
                    @Override
                    public void accept(Disposable disposable) throws Exception {
                        timerDisposable = disposable;
                    }
                })
                .subscribe(new SimpleObserver<Long>() {
                    @Override
                    public void onNext(@NonNull Long l) {
                        notifyTimer(l);
                    }

                    @Override
                    public void onComplete() {
                        super.onComplete();
                        notifyTimer(0);
                    }
                });
    }

    private OrderApi getOrderLoader() {
        return RetrofitIml.get().getHttpService(OrderApi.class);
    }

    private void notifyTimer(long diff) {
        if (!shouldDisplayTimerBanner()) {
            dismissTimer();
            return;
        }
        countdown = diff;
        if (diff > 0) {
            h = (int) (diff / 60 / 60);//时
            m = (int) (diff / 60 % 60); //分
            s = (int) (diff % 60);//秒

            if (listeners != null) {
                for (TimerChangedListener listener : listeners) {
                    dispatch(listener, true, h, m, s);
                }
            }
        } else {
            onTimerEnd();
        }
    }

    private void onTimerEnd() {
        dismissTimer();
        //重新请求
        refreshTimerBanner(false);
    }

    public void registerAndLog(TimerChangedListener listener, View view) {
        registerTimerChangedListener(listener);
        logImp(view);
    }

    public void registerTimerChangedListener(TimerChangedListener listener) {
        if (listener != null && !listeners.contains(listener)) {
            if (isDismissByUser()) {
                listener.onChanged(false, -1, -1, -1);
                return;
            }
            listener.onRegister();
            if (isRunningTimer()) {
                listener.onChanged(true, h, m, s);
            } else if (isIdleTimer()) {
                listener.onChanged(true, 0, 0, 0);
            } else if (!shouldDisplayTimerBanner()) {
                listener.onChanged(false, -1, -1, -1);
            }
            listeners.add(listener);
        }
    }

    public void unregisterTimerChangedListener(TimerChangedListener listener) {
        if (listener != null) {
            listeners.remove(listener);
        }
    }

    public void setTimerPage(View view, String pageName) {
        if (view instanceof TimerBannerView) {
            ((TimerBannerView) view).setPageName(pageName);
        }
    }

    public void setTimerInfo(View view, boolean display, int hour, int min, int sec) {
        if (view instanceof TimerBannerView) {
            ((TimerBannerView) view).setTimerContent(targetData, display, hour, min, sec);
        }
    }

    private void dispatch(TimerChangedListener listener, boolean display, int hour, int min, int sec) {
        if (listener != null) {
            TaskScheduler.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    listener.onChanged(display, hour, min, sec);
                }
            });
        }
    }

    public long getCountdown() {
        return countdown;
    }

    /**
     * 用户主动关闭倒计时
     */
    public void dismissTimerByUser() {
        dismissByUser = true;
        dismissTimer();
        uploadData();
    }

    private void dismissTimer() {
        if (timerDisposable != null && !timerDisposable.isDisposed()) {
            timerDisposable.dispose();
        }
        if (listeners != null) {
            for (TimerChangedListener listener : listeners) {
                dispatch(listener, false, 0, 0, 0);
            }
        }
    }

    private boolean shouldDisplayTimerBanner() {
        boolean display = targetData != null;
        if (display) {
            int countdownType = targetData.getCountdownType();
            return countdownType == TYPE_IDLE || targetData.getCountdownValue() > 0;
        }
        return false;
    }

    public boolean isDismissByUser() {
        return dismissByUser;
    }

    private boolean isRunningTimer() {
        return timerDisposable != null && !timerDisposable.isDisposed();
    }

    private boolean isIdleTimer() {
        return shouldDisplayTimerBanner() && targetData.isIdleTimer();
    }

    private void loadData() {
        getOrderLoader()
                .getTimerBanner()
                .compose(DisposableTransformer.scheduler(true))
                .subscribe(new ResponseObserver<ResponseBean<List<TimerBannerBean>>>() {
                    @Override
                    public void onResponse(ResponseBean<List<TimerBannerBean>> response) {
                        List<TimerBannerBean> list = response.getData();
                        targetData = !EmptyUtils.isEmpty(list) ? list.get(0) : null;
                        prepareTimer();
                    }
                });
    }

    private void uploadData() {
        if (targetData != null) {
            getOrderLoader()
                    .dismissTimerBanner(new RequestParams().put("bar_id", targetData.id).get())
                    .compose(DisposableTransformer.scheduler(false))
                    .subscribe(new ResponseObserver<SimpleResponseBean>() {
                        @Override
                        public void onResponse(SimpleResponseBean response) {
                        }
                    });
        }
    }

    private void prepareTimer() {
        boolean displayTimerBanner = shouldDisplayTimerBanner();
        intervalTime = 0;
        countdown = 0;
        if (displayTimerBanner) {
            intervalTime = targetData.getCountdownValue();
        }
        if (intervalTime > 0) {
            prepareStartTimer();
        } else if (isIdleTimer()) {
            prepareIdleTimer();
        } else {
            dismissTimer();
        }
    }

    public void mockData() {
        targetData = new TimerBannerBean();
        targetData.color = "#FFFFFF";
        targetData.bg_color = "#2F92FF";
        targetData.bg_img = "";
//        targetData.icon_url = "https://img2.baidu.com/it/u=1506619095,3241193239&fm=253&fmt=auto&app=138&f=GIF?w=100&h=100";

        targetData.title = new TimerBannerBean.Title();
        targetData.title.text = "下单享受好礼";
        targetData.title.sub_text = "好礼享受不停";

        targetData.close_cta = new TimerBannerBean.CloseCta();
        targetData.close_cta.visible = true;
        targetData.close_cta.icon_url = "https://img06.weeecdn.com/common/promotion/close.png";
        targetData.close_cta.link = "";

//        targetData.countdown = new TimerBannerBean.Countdown();
//        targetData.countdown.number_bg_color = "#FFFFFF";
//        targetData.countdown.number_color = "#2F92FF";
//        targetData.countdown.show_time = 10;
//        targetData.countdown.type = 3;
//        targetData.countdown.server_timestamp = 1679476830L;
//        targetData.countdown.end_time = 1679476830L + 10;
//        targetData.countdown.title = "End in";

        targetData.right_cta = new TimerBannerBean.RightCta();
        targetData.right_cta.title = "Verify";

        targetData.pages = Arrays.asList("page_home", "page_me");
        dismissByUser = false;
        prepareTimer();
    }

    private void logImp(View view) {
        if (targetData != null && view != null && view.getVisibility() == View.VISIBLE) {//target data null check
            AppAnalytics.logBannerImp(
                    new EagleTrackModel.Builder()
                            .setMod_nm(EagleTrackEvent.ModNm.PROMOTION_BAR)
                            .setBannerId(targetData.id)
                            .setUrl(targetData.link)
                            .build()
                            .getParams());
        }
    }

    /**
     * 加购后需要请求timerBanner接口
     * @return boolean
     */
    public boolean isProgressStyle() {
        return targetData != null && "progress".equalsIgnoreCase(targetData.style);
    }

    public boolean isDisplayStyleDone() {
        return isProgressStyle() && targetData.isDisplayStyleDone();
    }
}