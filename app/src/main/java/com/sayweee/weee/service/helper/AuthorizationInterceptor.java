package com.sayweee.weee.service.helper;

import com.sayweee.weee.global.manager.OrderManager;
import com.sayweee.weee.module.launch.SplashActivity;
import com.sayweee.wrapper.helper.lifecycle.LifecycleProvider;

import org.jetbrains.annotations.NotNull;

import java.io.IOException;

import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;

/**
 * Author:  winds
 * Email:   <EMAIL>
 * Date:    2021/2/19.
 * Desc:
 */
public class AuthorizationInterceptor implements Interceptor {

    private long last;

    @NotNull
    @Override
    public Response intercept(Chain chain) throws IOException {
        //更新simple信息，simple接口上添加服务端当前时间和服务端截单时间，前端计算出本地截单时间，在本地请求接口时判断是否超出截单时间，若超出刷新simple接口，同时强刷当前页面
        OrderManager.get().checkExpireDtm();

        Request request = chain.request();
        Response response = chain.proceed(request);
        //401 403 问题处理
        if ((response.code() == 401 || response.code() == 403)) {
            if (shouldRecreate()) {
                // token recreate
                long millis = System.currentTimeMillis();
                if (millis - last > 20 * 1000) { //20s内只创建一次token
                    last = millis;
                    TokenHelper.createAnonymousToken();
                }
            }
        }
        return response;
    }

    protected boolean shouldRecreate() {
        return !(LifecycleProvider.get().getTopActivity() instanceof SplashActivity);
    }
}
