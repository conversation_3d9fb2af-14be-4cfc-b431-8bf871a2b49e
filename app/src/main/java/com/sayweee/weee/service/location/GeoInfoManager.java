package com.sayweee.weee.service.location;

import android.Manifest;
import android.app.Activity;
import android.location.Location;

import com.sayweee.scheduler.TaskScheduler;
import com.sayweee.service.ConfigService;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.service.config.ConfigManager;
import com.sayweee.weee.service.config.bean.AddressPositioningConfigBean;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.wrapper.helper.lifecycle.LifecycleProvider;
import com.sayweee.wrapper.utils.PermissionUtils;

import java.util.List;

/**
 * Desc:    当前设备经纬度地理信息
 */
public class GeoInfoManager {

    private static final GeoInfoManager sInstance = new GeoInfoManager();

    private Location location;
    private boolean hasLocatedOnHome;

    public static GeoInfoManager get() {
        return sInstance;
    }

    private GeoInfoManager() {
    }

    public void setGeoInfo(Location location) {
        this.location = location;
    }

    public Location getGeoInfo() {
        return location;
    }

    public void setHasLocatedOnHome(boolean result) {
        this.hasLocatedOnHome = result;
    }

    public boolean hasLocatedOnHome() {
        return hasLocatedOnHome;
    }

    public void execLocateRefresh() {
        Activity activity = LifecycleProvider.get().getTopActivity();
        if (activity != null && AccountManager.get().needLocateRefresh()) {
            AddressPositioningConfigBean config = ConfigService.get().getDynamicConfig(ConfigManager.DynamicConfig.ADDRESS_POSITIONING);
            if (config != null && config.foreground_positioning_disable) {
                return;
            }
            TaskScheduler.getMainHandler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    String[] permissions = {Manifest.permission.ACCESS_FINE_LOCATION, Manifest.permission.ACCESS_COARSE_LOCATION};
                    List<String> list = PermissionUtils.checkMorePermissions(activity, permissions);
                    if (EmptyUtils.isEmpty(list) && CommonTools.isGpsOpen(activity)) {
                        LocationUtils.startLocation(new SimpleLocationListener() {
                            @Override
                            public void onLocationSuccess(Location location) {
                                LocationUtils.stopLocation(this);
                            }

                            @Override
                            public void onLocationFailed(Exception e) {
                                super.onLocationFailed(e);
                                LocationUtils.stopLocation(this);
                            }
                        });
                    }
                }
            }, 1000);
        }
    }
}
