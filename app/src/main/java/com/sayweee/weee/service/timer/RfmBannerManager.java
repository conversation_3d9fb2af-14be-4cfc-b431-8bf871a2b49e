package com.sayweee.weee.service.timer;

import android.view.View;

import com.sayweee.scheduler.TaskScheduler;
import com.sayweee.weee.module.cart.service.OrderApi;
import com.sayweee.weee.module.home.bean.RfmBean;
import com.sayweee.weee.service.rx.DisposableTransformer;
import com.sayweee.weee.widget.RfmBannerView;
import com.sayweee.wrapper.bean.ResponseBean;
import com.sayweee.wrapper.core.compat.ResponseObserver;
import com.sayweee.wrapper.http.RetrofitIml;

/**
 * Author:  ycy
 */
public class RfmBannerManager {

    public static RfmBannerManager get() {
        return Builder.instance;
    }

    private static final class Builder {
        private static final RfmBannerManager instance = new RfmBannerManager();
    }

    private OrderApi getOrderLoader() {
        return RetrofitIml.get().getHttpService(OrderApi.class);
    }

    RmfChangedListener listener;

    public interface RmfChangedListener {
        void onChanged(RfmBean.AndroidRFMCouponHomeBean rfm);
    }

    public void registerListener(RmfChangedListener rmfChangedListener) {
        this.listener = rmfChangedListener;
        getOrderLoader()
                .getRfmBanner()
                .compose(DisposableTransformer.scheduler(false))
                .subscribe(new ResponseObserver<ResponseBean<RfmBean>>() {
                    @Override
                    public void onResponse(ResponseBean<RfmBean> response) {
                        dispatch(listener,response.object.android_RFMCoupon_home);
                    }
                });
    }

    private void dispatch(RmfChangedListener listener, RfmBean.AndroidRFMCouponHomeBean rfm) {
        if (listener != null && rfm != null) {
            TaskScheduler.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    listener.onChanged(rfm);
                }
            });
        }
    }

    public void show(View view, RfmBean.AndroidRFMCouponHomeBean rfm) {
        if (view instanceof RfmBannerView) {
            RfmBannerView rfmBannerView = (RfmBannerView) view;
            rfmBannerView.setInfo(rfm);
        }
    }

    public void unregisterListener() {
        listener = null;
    }
}