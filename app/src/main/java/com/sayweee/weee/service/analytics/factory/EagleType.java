package com.sayweee.weee.service.analytics.factory;

/**
 * Author:  winds
 * Date:    3/23/22.
 * Desc:
 */
public enum EagleType {

    TYPE_CARD(10), //标准卡片
    TYPE_LIST(20), //标准列表
    TYPE_ITEM_SMALL(30), //小号卡片
    TYPE_ITEM_MEDIUM(40),  //中号卡片
    TYPE_ITEM_POST(50),  //视频底部卡片
    TYPE_DEAL_OF_WEEK(60), //一周一品
    TYPE_ITEM_LIGHTING_DEAL(70), //秒杀

    TYPE_ITEM_CART_INVALID_PRODUCT(80), //购物车无效商品
    TYPE_PDP(90); //PDP

    private final int type;

    EagleType(int type) {
        this.type = type;
    }

    public int getType() {
        return type;
    }
}
