package com.sayweee.weee.service.pay;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ResolveInfo;

import androidx.annotation.NonNull;
import androidx.fragment.app.FragmentActivity;

import com.braintreepayments.api.BraintreeClient;
import com.braintreepayments.api.DataCollector;
import com.braintreepayments.api.DataCollectorCallback;
import com.braintreepayments.api.DataCollectorRequest;
import com.braintreepayments.api.PayPalClient;
import com.braintreepayments.api.VenmoClient;

import java.util.List;

public class BraintreePaymentHelper {

    private static final String VENMO_APP_PACKAGE = "com.venmo";
    private static final String VENMO_APP_SWITCH_ACTIVITY = "controller.SetupMerchantActivity";

    private BraintreeClient braintreeClient;
    private DataCollector dataCollector;
    private VenmoClient venmoClient;
    private PayPalClient payPalClient;

    private String deviceDataCache;

    @NonNull
    public BraintreeClient getBraintreeClient(@NonNull Context context) {
        BraintreeClient client = ensureBraintreeClient(context);
        ensureDataCollector(context);
        return client;
    }

    public void initPayPalClient(@NonNull FragmentActivity activity, @NonNull String returnUrlSchema) {
        if (payPalClient == null) {
            braintreeClient = new BraintreeClient(activity, new BraintreeClientTokenProvider(), returnUrlSchema);
            payPalClient = new PayPalClient(activity, braintreeClient);
        }
    }

    @NonNull
    public PayPalClient getPayPalClient() {
        if (payPalClient == null) {
            throw new IllegalStateException("Please init PayPalClient first!");
        }
        return payPalClient;
    }

    @NonNull
    public VenmoClient getVenmoClient(@NonNull FragmentActivity activity) {
        return ensureVenmoClient(activity);
    }

    public void collectDeviceData(@NonNull Context context, @NonNull DataCollectorCallback callback) {
        collectDeviceData(context, true, callback);
    }

    public void collectDeviceData(
            @NonNull Context context,
            boolean preferCache,
            @NonNull DataCollectorCallback callback
    ) {
        if (preferCache && deviceDataCache != null && !deviceDataCache.isEmpty()) {
            callback.onResult(deviceDataCache, null);
            return;
        }

        DataCollectorRequest dataCollectorRequest = new DataCollectorRequest(false);
        ensureDataCollector(context).collectDeviceData(context, dataCollectorRequest, (deviceData, error) -> {
            deviceDataCache = deviceData;
            callback.onResult(deviceData, error);
        });
    }

    public String getDeviceDataCache() {
        return deviceDataCache;
    }

    // ==================================================
    // Private methods

    @NonNull
    private BraintreeClient ensureBraintreeClient(@NonNull Context context) {
        if (braintreeClient == null) {
            braintreeClient = new BraintreeClient(context, new BraintreeClientTokenProvider());
        }
        return braintreeClient;
    }

    @NonNull
    private DataCollector ensureDataCollector(@NonNull Context context) {
        if (dataCollector == null) {
            dataCollector = new DataCollector(ensureBraintreeClient(context));
        }
        return dataCollector;
    }

    @NonNull
    private VenmoClient ensureVenmoClient(@NonNull FragmentActivity activity) {
        if (venmoClient == null) {
            venmoClient = new VenmoClient(activity, ensureBraintreeClient(activity));
        }
        return venmoClient;
    }

    public static boolean isVenmoInstalled(@NonNull Context context) {
        Intent intent = new Intent();
        intent.setComponent(new ComponentName(VENMO_APP_PACKAGE, VENMO_APP_PACKAGE + "." + VENMO_APP_SWITCH_ACTIVITY));
        List<ResolveInfo> activities;
        try {
            activities = context.getPackageManager().queryIntentActivities(intent, 0);
        } catch (Exception ignored) {
            activities = null;
        }
        return activities != null && activities.size() == 1;
    }

}
