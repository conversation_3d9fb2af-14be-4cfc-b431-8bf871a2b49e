package com.sayweee.weee.service.analytics;

import android.graphics.Rect;
import android.view.View;

import androidx.recyclerview.widget.RecyclerView;

/**
 * Author:  winds
 * Date:    2022/5/18.
 * Desc:
 */
public class ImpressionHelper {

    public static boolean isVisible(View view) {
        if (view != null) {
            Rect rect = new Rect();
            return view.getLocalVisibleRect(rect);
        }
        return false;
    }

    public static boolean isImpressionReportEnable(RecyclerView view) {
        if (view != null) {
            Rect rect = new Rect();
            boolean isVisible = view.getLocalVisibleRect(rect);
            return isVisible && rect.height() * 2 > view.getHeight();
        }
        return false;
    }

    /**
     * 垂直方向的item是否可见
     * @param view
     * @return
     */
    public static boolean isImpressionEnableOnVertical(View view) {
        if (view != null) {
            Rect rect = new Rect();
            boolean isVisible = view.getLocalVisibleRect(rect);
            return isVisible && rect.height() * 2 > view.getHeight();
        }
        return false;
    }

    /**
     * 水平方向的item是否可见
     * @param view
     * @return
     */
    public static boolean isImpressionEnableOnHorizontal(View view) {
        if (view != null) {
            Rect rect = new Rect();
            boolean isVisible = view.getLocalVisibleRect(rect);
            return isVisible && rect.width() * 2 > view.getWidth();
        }
        return false;
    }

    /**
     * 水平和垂直方面均是否可见
     * @param view
     * @return
     */
    public static boolean isImpressionEnableOnOnHorizontalAndVertical(View view) {
        if (view != null) {
            Rect rect = new Rect();
            boolean isVisible = view.getLocalVisibleRect(rect);
            return isVisible && rect.height() * 2 > view.getHeight() && rect.width() * 2 > view.getWidth();
        }
        return false;
    }

}
