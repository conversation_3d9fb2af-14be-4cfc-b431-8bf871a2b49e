package com.sayweee.weee.service.helper;

import android.app.Activity;
import android.content.Context;
import android.content.DialogInterface;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Typeface;
import android.graphics.drawable.Drawable;
import android.view.Gravity;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.core.content.ContextCompat;

import com.sayweee.weee.R;
import com.sayweee.weee.global.config.AppConfig;
import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.module.checkout.bean.PreCheckoutBean;
import com.sayweee.weee.module.dialog.FeeTipsDialog;
import com.sayweee.weee.module.popup.PopupSlideDialog;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.service.track.WeeeEvent;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.widget.WrapperPopWindow;
import com.sayweee.weee.widget.op.BubbleLayout;
import com.sayweee.wrapper.base.view.ViewHelper;
import com.sayweee.wrapper.base.view.WrapperDialog;
import com.sayweee.wrapper.helper.lifecycle.LifecycleProvider;
import com.sayweee.wrapper.listener.OnViewHelper;

import java.util.ArrayList;
import java.util.List;

/**
 * Author:  winds
 * Date:    2021/11/8.
 * Desc:
 */
public class PromoHelper {

    public static boolean showAlcoholIntroduce(Context context, View view, int extra, String title, String content) {
        if (context != null && view != null && view.isAttachedToWindow()) {
            view.postDelayed(new Runnable() {
                @Override
                public void run() {
                    execShowAlcoholIntroduce(context, view, 5, title, content);
                }
            }, 150);
            return true;
        }
        return false;
    }

    private static void execShowAlcoholIntroduce(Context context, View view, int extra, String title, String content) {
        if (context != null && view != null && view.isAttachedToWindow()) {
            int[] location = new int[2];
            view.getLocationInWindow(location);
            int width = CommonTools.dp2px(246);
            int height = CommonTools.dp2px(145);

            if (content != null) {
                Paint paint = new Paint();
                paint.setTextSize(CommonTools.sp2px(15));
                float textWidth = paint.measureText(content);

                Paint.FontMetrics fontMetrics = paint.getFontMetrics();
                float textHeight = fontMetrics.bottom - fontMetrics.top;
                int itemWidth = width - CommonTools.dp2px(32);
                int line = (int) textWidth / itemWidth + 1;

                int temp = (int) (line * textHeight) + CommonTools.dp2px(75);

                height = Math.max(temp, CommonTools.dp2px(120));
            }

            int xOff = width - view.getWidth();
            int yOff = height + CommonTools.dp2px(extra);
            int x, y;
            int xDiff = 0;
            int yDiff = 0;
            boolean reverse = false;
            if (location[0] > xOff) {
                int diff = CommonTools.dp2px(7);
                x = location[0] - xOff + diff;
            } else {
                x = CommonTools.dp2px(12);
                xDiff = location[0] - xOff - x;
            }
            if (location[1] - CommonTools.getStatusBarHeight(context) > yOff) {
                y = location[1] - yOff;
            } else {
                reverse = true;
                y = location[1] + CommonTools.dp2px(extra) + view.getHeight();
            }
            int finalXDiff = xDiff;
            int layoutRes = reverse ? R.layout.dialog_promo_reverse : R.layout.dialog_promo;
            WrapperPopWindow popWindow = new WrapperPopWindow.PopupWindowBuilder(context)
                    .setView(ViewTools.getHelperView(context, layoutRes, new OnViewHelper() {
                        @Override
                        public void help(ViewHelper helper) {
                            ((LinearLayout) helper.getView(R.id.layout_bubble_inner)).setGravity(Gravity.LEFT);
                            BubbleLayout layoutBubble = helper.getView(R.id.layout_bubble);
                            if (finalXDiff != 0) {
                                layoutBubble.setTriangleOffset(finalXDiff + layoutBubble.getTriangleOffset());
                            }
                            ((TextView) helper.getView(R.id.tv_title)).setTypeface(Typeface.defaultFromStyle(Typeface.BOLD_ITALIC));
                            helper.setTextColor(R.id.tv_title, Color.parseColor("#6BD9C7"));
                            helper.setText(R.id.tv_title, title);
                            helper.setText(R.id.tv_content, content);
                        }
                    }))
                    .size(width, height)
                    .setFocusable(true)
                    .setOutsideTouchable(true)
                    .create();
            Activity activity;
            if (context instanceof Activity) {
                activity = (Activity) context;
            } else {
                activity = LifecycleProvider.get().getTopActivity();
            }
            if (activity == null) {
                return;
            }
            View decorView = activity.getWindow().getDecorView();
            popWindow.showAtLocation(decorView, Gravity.TOP | Gravity.LEFT, x, y);
            decorView.postDelayed(new Runnable() {
                @Override
                public void run() {
                    if (popWindow != null && popWindow.isShow()) {
                        popWindow.dismiss();
                    }
                }
            }, 3000);
        }
    }

    public static Drawable getTermsDrawable(Context context) {
        return getDrawableByBounds(context, R.mipmap.ic_terms, 22, 22);
    }

    public static Drawable getDrawableByBounds(Context context, int drawableRes, float width, float height) {
        Drawable drawable = ContextCompat.getDrawable(context, drawableRes);
        if (drawable != null) {
            drawable.setBounds(0, 0, CommonTools.dp2px(width), CommonTools.dp2px(height));
        }
        return drawable;
    }

    public static void showFeeTips(List<PreCheckoutBean.AdditionalFee> list) {
        Activity context = LifecycleProvider.get().getActivity();
        if (context != null && !EmptyUtils.isEmpty(list)) {
            List<List<String>> packet = new ArrayList<>();
            for (PreCheckoutBean.AdditionalFee data : list) {
                ArrayList<String> item = new ArrayList<>();
                item.add(data.title_desc);
                item.add(data.title_summary);
                packet.add(item);
            }
            new FeeTipsDialog(context).showTips(context.getString(R.string.s_checkout_fee_include), packet).show();
        }
    }

    public static void showColdPackageTips() {
        Activity activity = LifecycleProvider.get().getTopActivity();
        if (activity != null) {
            new PopupSlideDialog().loadUrl(AppConfig.HOST_WEB + Constants.Url.COLD_PACK_INTRO).show();
        }
    }

    public static void showPdpColdPackageTips() {
        Activity activity = LifecycleProvider.get().getTopActivity();
        if (activity != null) {
            WrapperDialog dialog = new PopupSlideDialog().loadUrl(AppConfig.HOST_WEB + Constants.Url.COLD_PACK_INTRO).show();
            dialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
                @Override
                public void onDismiss(DialogInterface dialog) {
                    AppAnalytics.logPageView(WeeeEvent.PageView.PRODUCT, activity);
                }
            });
        }
    }


}
