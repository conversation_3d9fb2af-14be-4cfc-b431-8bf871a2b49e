package com.sayweee.weee.service.pay;

import androidx.annotation.NonNull;

import com.braintreepayments.api.ClientTokenCallback;
import com.braintreepayments.api.ClientTokenProvider;
import com.sayweee.weee.module.cart.service.OrderApi;
import com.sayweee.wrapper.bean.FailureBean;
import com.sayweee.wrapper.bean.ResponseBean;
import com.sayweee.wrapper.core.compat.ResponseObserver;
import com.sayweee.wrapper.core.compat.ResponseTransformer;
import com.sayweee.wrapper.http.RetrofitIml;

/**
 * Author:  winds
 * Date:    2022/12/22.
 * Desc:
 */
public class BraintreeClientTokenProvider implements ClientTokenProvider {

    @Override
    public void getClientToken(@NonNull ClientTokenCallback callback) {
        RetrofitIml.get().getHttpService(OrderApi.class)
                .generateBraintreeToken()
                .compose(ResponseTransformer.scheduler())
                .subscribe(new ResponseObserver<ResponseBean<String>>() {
                    @Override
                    public void onResponse(ResponseBean<String> response) {
                        if (callback != null) {
                            callback.onSuccess(response.getData());
                        }
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        super.onError(failure);
                        if (callback != null) {
                            callback.onFailure(failure.getException());
                        }
                    }
                });
    }
}
