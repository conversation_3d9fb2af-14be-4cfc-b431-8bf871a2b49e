package com.sayweee.weee.service.location;

import android.app.Application;
import android.graphics.Bitmap;
import android.view.View;
import android.widget.ImageView;

import com.google.android.gms.maps.CameraUpdate;
import com.google.android.gms.maps.CameraUpdateFactory;
import com.google.android.gms.maps.GoogleMap;
import com.google.android.gms.maps.GoogleMapOptions;
import com.google.android.gms.maps.MapView;
import com.google.android.gms.maps.OnMapReadyCallback;
import com.google.android.gms.maps.model.BitmapDescriptorFactory;
import com.google.android.gms.maps.model.LatLng;
import com.google.android.gms.maps.model.Marker;
import com.google.android.gms.maps.model.MarkerOptions;
import com.google.android.gms.maps.model.PolylineOptions;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.wrapper.helper.lifecycle.LifecycleProvider;

import java.util.LinkedList;
import java.util.List;

/**
 * Author:  winds
 * Date:    12/22/21.
 * Desc:
 */
public class MapDecoder {

    public static void addMarker(GoogleMap map, LatLng latLng, Bitmap bitmap) {
        MarkerOptions markerOptions = new MarkerOptions();
        markerOptions.position(latLng).icon(BitmapDescriptorFactory.fromBitmap(bitmap)).draggable(false).anchor(1, 1);
        Marker marker = map.addMarker(markerOptions);
        map.setOnMarkerClickListener(new GoogleMap.OnMarkerClickListener() {
            @Override
            public boolean onMarkerClick(Marker marker) {
                return false;
            }
        });
    }

    public static boolean isVisible(Marker marker) {
        return marker != null && marker.isVisible();
    }

    public static void addPolyline(GoogleMap map, List<LatLng> list, int color) {
        if (list != null && list.size() > 1) {
            for (int i = 0; i < list.size() - 1; i++) {
                LatLng current = list.get(i);
                LatLng next = list.get(i + 1);

                PolylineOptions mPolylineOptions = new PolylineOptions();
                mPolylineOptions.width(CommonTools.dp2px(4));
                mPolylineOptions.color(color);
                mPolylineOptions.add(current);
                mPolylineOptions.add(next);
                mPolylineOptions.zIndex(7f);

                map.addPolyline(mPolylineOptions);
            }
        }
    }

    public static void decodeBackground() {
        Application application = LifecycleProvider.get().getApplication();
        GoogleMapOptions options = new GoogleMapOptions()
                .compassEnabled(false)
                .mapToolbarEnabled(false)
                .liteMode(true);
        MapView mapView = new MapView(application, options);
        mapView.onCreate(null);
        mapView.getMapAsync(new OnMapReadyCallback() {
            @Override
            public void onMapReady(GoogleMap googleMap) {
                int width = CommonTools.dp2px(300);
                int height = CommonTools.dp2px(100);
                mapView.measure(View.MeasureSpec.makeMeasureSpec(width, View.MeasureSpec.EXACTLY), View.MeasureSpec.makeMeasureSpec(height, View.MeasureSpec.EXACTLY));
                mapView.layout(0, 0, width, height);
            }
        });
    }

    public static void decodeBackgroundByTask(LinkedList<MapDecoderTask> list) {
        Application application = LifecycleProvider.get().getApplication();
        GoogleMapOptions options = new GoogleMapOptions()
                .compassEnabled(false)
                .mapToolbarEnabled(false)
                .liteMode(true);
        MapView mapView = new MapView(application, options);
        mapView.onCreate(null);
        mapView.getMapAsync(new OnMapReadyCallback() {
            @Override
            public void onMapReady(GoogleMap googleMap) {
                int width = CommonTools.dp2px(300);
                int height = CommonTools.dp2px(100);
                mapView.measure(View.MeasureSpec.makeMeasureSpec(width, View.MeasureSpec.EXACTLY), View.MeasureSpec.makeMeasureSpec(height, View.MeasureSpec.EXACTLY));
                mapView.layout(0, 0, width, height);
                decodeOnMapReadyByTask(googleMap, list);
            }
        });
    }

    public static void decodeOnMapReadyByTask(GoogleMap googleMap, LinkedList<MapDecoderTask> list) {
        if (!list.isEmpty()) {
            MapDecoderTask task = list.pollFirst();
            MapDecoder.addMarker(googleMap, task.latLng, task.displayBitmap);
            CameraUpdate cameraUpdate = CameraUpdateFactory.newLatLngZoom(task.latLng, 18);
            googleMap.moveCamera(cameraUpdate);
            googleMap.setOnMapLoadedCallback(new GoogleMap.OnMapLoadedCallback() {
                @Override
                public void onMapLoaded() {
                    googleMap.snapshot(new GoogleMap.SnapshotReadyCallback() {
                        @Override
                        public void onSnapshotReady(Bitmap bitmap) {
                            ImageView imageView = task.imageViewRef.get();
                            if (imageView != null) {
                                imageView.setVisibility(View.VISIBLE);
                                imageView.setImageBitmap(bitmap);
                            }
                            decodeOnMapReadyByTask(googleMap, list);
                        }
                    });
                }
            });
        }
    }

    public static void decodeOnMapReady(GoogleMap googleMap, LatLng latLng, Bitmap displayBmp, ImageView imageView) {
        MapDecoder.addMarker(googleMap, latLng, displayBmp);
        CameraUpdate cameraUpdate = CameraUpdateFactory.newLatLngZoom(latLng, 18);
        googleMap.moveCamera(cameraUpdate);
        googleMap.setOnMapLoadedCallback(new GoogleMap.OnMapLoadedCallback() {
            @Override
            public void onMapLoaded() {
                googleMap.snapshot(new GoogleMap.SnapshotReadyCallback() {
                    @Override
                    public void onSnapshotReady(Bitmap bitmap) {
                        imageView.setVisibility(View.VISIBLE);
                        imageView.setImageBitmap(bitmap);
                    }
                });
            }
        });
    }
}
