package com.sayweee.weee.service.location;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.pm.PackageManager;
import android.location.Address;
import android.location.Criteria;
import android.location.Geocoder;
import android.location.Location;
import android.location.LocationListener;
import android.location.LocationManager;

import androidx.core.app.ActivityCompat;

import com.sayweee.logger.Logger;
import com.sayweee.weee.utils.DecimalTools;
import com.sayweee.wrapper.core.compat.SimpleObserver;
import com.sayweee.wrapper.helper.lifecycle.LifecycleProvider;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

import io.reactivex.Observable;
import io.reactivex.ObservableEmitter;
import io.reactivex.ObservableOnSubscribe;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.functions.Function;
import io.reactivex.functions.Predicate;
import io.reactivex.schedulers.Schedulers;

/**
 * Author:  winds
 * Email:   <EMAIL>
 * Date:    2020/10/29.
 * Desc:
 */
public class LocationUtils {

    static android.location.LocationManager manager;

    public static void startLocation(SimpleLocationListener listener) {
        if (listener == null) {
            return;
        }
        if (manager == null) {
            //实例化Location应用管理类
            manager = (android.location.LocationManager) LifecycleProvider.get().getApplication().getSystemService(Context.LOCATION_SERVICE);
        }
        if (manager == null) {
            listener.onLocationFailed(new IllegalStateException("location service failed"));
            return;
        }
        Activity activity = LifecycleProvider.get().getActivity();
        if (ActivityCompat.checkSelfPermission(activity, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED
                && ActivityCompat.checkSelfPermission(activity, Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
            listener.onLocationFailed(new IllegalStateException("no permission"));
            return;
        }
        Criteria criteria = new Criteria();
        // 设置定位精确度 Criteria.ACCURACY_COARSE比较粗略，Criteria.ACCURACY_FINE则比较精细  
        criteria.setAccuracy(Criteria.ACCURACY_COARSE);
        // 设置是否要求速度  
        criteria.setSpeedRequired(false);
        // 设置是否允许运营商收费  
        criteria.setCostAllowed(true);
        // 设置是否需要方位信息  
        criteria.setBearingRequired(false);
        // 设置是否需要海拔信息  
        criteria.setAltitudeRequired(false);
        // 设置对电源的需求  
        criteria.setPowerRequirement(Criteria.POWER_LOW);
        String provider;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
            //高版本增加混合定位逻辑
            provider = LocationManager.FUSED_PROVIDER;
        } else {
            provider = manager.getBestProvider(criteria, true);
            if (LocationManager.GPS_PROVIDER.equals(provider) && !isGpsProviderEnable()) {
                Logger.f("location").i("GPS locate disable");
                provider = LocationManager.NETWORK_PROVIDER;
            }
            if (LocationManager.NETWORK_PROVIDER.equals(provider) && !isNetworkProviderEnable()) {
                Logger.f("location").i("network locate disable");
                provider = LocationManager.GPS_PROVIDER;
            }
        }
        if (provider == null) {
            provider = LocationManager.NETWORK_PROVIDER;
        }
        try {
            //noinspection ConstantValue
            if (provider != null && listener != null) {
                manager.requestSingleUpdate(provider, listener, null);
            }
        } catch (Exception ex) {
            listener.onLocationFailed(ex);
        }
    }

    public static Observable<Address> parseZipCode(Location location) {
        return Observable.create(new ObservableOnSubscribe<List<Address>>() {
                    @Override
                    public void subscribe(ObservableEmitter<List<Address>> emitter) throws Exception {
                        if (location == null) {
                            emitter.onError(new NullPointerException("location is null"));
                        } else {
                            try {
                                double lat = location.getLatitude();
                                double lon = location.getLongitude();
                                Geocoder geocoder = new Geocoder(LifecycleProvider.get().getApplication());
                                List<Address> list = geocoder.getFromLocation(lat, lon, 1);
                                emitter.onNext(list);
                            } catch (Exception ignored) {
                                emitter.onNext(new ArrayList<>());
                            }
                            emitter.onComplete();
                        }
                    }
                })
                .map(new Function<List<Address>, Address>() {
                    @Override
                    public Address apply(List<Address> list) throws Exception {
                        if (list == null || list.isEmpty()) {
                            throw new NullPointerException("location is null");
                        }
                        return list.get(0);
                    }
                })
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread());
    }


    public static void stopLocation(LocationListener listener) {
        if (manager != null && listener != null) {
            manager.removeUpdates(listener);
        }
    }

    public static void testNearbyAddress() {
        Observable.create(new ObservableOnSubscribe<List<Address>>() {
                    @Override
                    public void subscribe(ObservableEmitter<List<Address>> emitter) throws Exception {
                        double lat = 37.8905824;
                        double lon = -122.2879408;
                        Geocoder geocoder = new Geocoder(LifecycleProvider.get().getApplication());
                        List<Address> list = geocoder.getFromLocation(lat, lon, 5);
                        emitter.onNext(list);
                        emitter.onComplete();
                    }
                })
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new SimpleObserver<List<Address>>() {
                    @Override
                    public void onNext(List<Address> addresses) {
                        super.onNext(addresses);
                        Logger.toJson(addresses);
                    }
                });
    }

    public static Observable<Address> getTestZipCode() {
        return Observable.create(new ObservableOnSubscribe<List<Address>>() {
                    @Override
                    public void subscribe(ObservableEmitter<List<Address>> emitter) throws Exception {
                        double lat = 37.8905824;
                        double lon = -122.2879408;
                        Geocoder geocoder = new Geocoder(LifecycleProvider.get().getApplication());
                        List<Address> list = geocoder.getFromLocation(lat, lon, 1);
                        emitter.onNext(list);
                        emitter.onComplete();
                    }
                }).map(new Function<List<Address>, Address>() {
                    @Override
                    public Address apply(List<Address> list) throws Exception {
                        if (list != null && list.size() > 0) {
                            return list.get(0);
                        }
                        return null;
                    }
                })
                .filter(new Predicate<Address>() {
                    @Override
                    public boolean test(Address address) throws Exception {
                        return address != null && address.getPostalCode() != null;
                    }
                })
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread());
    }

    public static Observable<List<Address>> getNearbyAddress(Context activity, double lat1, double lon1) {
//        double lat = 37.8905824;
//        double lon = -122.2879408;
        return Observable.create(new ObservableOnSubscribe<List<Address>>() {
                    @Override
                    public void subscribe(ObservableEmitter<List<Address>> emitter) throws Exception {
                        Geocoder geocoder = new Geocoder(activity, Locale.US);
                        List<Address> list = geocoder.getFromLocation(lat1, lon1, 2);
                        Logger.json("===>", list);
                        if (list != null && list.size() > 0) {
                            emitter.onNext(list);
                            emitter.onComplete();
                        } else {
                            emitter.onError(new IllegalArgumentException(""));
                        }
                    }
                })
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread());
    }

    private static final double EARTH_RADIUS = 6378.137;// 地球赤道半径
    private static final double PI = 3.14159265;

    /**
     * π是弧度制，180度是角度制，d*π／180,表示每角度等于多少弧度。
     *
     * @param d
     * @return
     */
    private static double rad(double d) {
        return d * PI / 180.0;
    }

    /**
     * 根据两个点之间经纬度算之前的距离
     * 返回距离为英里
     * @param lng1
     * @param lat1
     * @param lng2
     * @param lat2
     * @return 单位：英里
     */
    public static double getDistance(double lng1, double lat1,
                                     double lng2, double lat2) {
        double radLat1 = rad(lat1);
        double radLat2 = rad(lat2);
        double a = radLat1 - radLat2;
        double b = rad(lng1) - rad(lng2);
        double s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2)
                + Math.cos(radLat1) * Math.cos(radLat2)
                * Math.pow(Math.sin(b / 2), 2)));
        s = s * EARTH_RADIUS;
        s = Math.round(s * 10000d) / 10000d;
        s = s * 1000;//米

        s = DecimalTools.divide(s, 1609.34);//英里
        return s;
    }

    public static boolean isDistanceReachLimit(double lng1, double lat1,
                                               double lng2, double lat2, int distance) {
        return getDistance(lng1, lat1, lng2, lat2) > distance;
    }

    public static boolean isDistanceWithinLimit(double lng1, double lat1, int distance) {
        Location geoInfo = GeoInfoManager.get().getGeoInfo();
        if (geoInfo == null || distance == 0) {
            return false;
        } else {
            return getDistance(lng1, lat1, geoInfo.getLongitude(), geoInfo.getLatitude()) <= distance;
        }
    }

    public static boolean isDistanceReachLimit(double lng1, double lat1, int distance) {
        Location geoInfo = GeoInfoManager.get().getGeoInfo();
        if (geoInfo == null || distance == 0) {
            return false;
        } else {
            return isDistanceReachLimit(lng1, lat1, geoInfo.getLongitude(), geoInfo.getLatitude(), distance);
        }
    }

    public static boolean isGpsProviderEnable() {
        try {
            return manager != null && manager.isProviderEnabled(LocationManager.GPS_PROVIDER);
        } catch (Exception e) {
            return false;
        }
    }

    public static boolean isNetworkProviderEnable() {
        try {
            return manager != null && manager.isProviderEnabled(LocationManager.NETWORK_PROVIDER);
        } catch (Exception e) {
            return false;
        }
    }
}
