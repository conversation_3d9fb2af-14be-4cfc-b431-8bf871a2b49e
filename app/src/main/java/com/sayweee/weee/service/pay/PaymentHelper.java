package com.sayweee.weee.service.pay;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.Application;

import androidx.annotation.NonNull;

import com.alipay.sdk.app.PayTask;
import com.google.android.gms.common.util.Hex;
import com.sayweee.logger.Logger;
import com.sayweee.weee.module.cart.service.OrderApi;
import com.sayweee.weee.module.checkout.bean.AliPayBean;
import com.sayweee.weee.module.checkout.bean.CashAppPayBean;
import com.sayweee.weee.module.checkout.bean.CitconPayBean;
import com.sayweee.weee.module.checkout.bean.PayBean;
import com.sayweee.weee.module.checkout.bean.PayPalPayBean;
import com.sayweee.weee.module.checkout.bean.PayStatusBean;
import com.sayweee.weee.module.checkout.bean.VenmoPayBean;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.wrapper.bean.FailureBean;
import com.sayweee.wrapper.bean.ResponseBean;
import com.sayweee.wrapper.bean.SimpleResponseBean;
import com.sayweee.wrapper.core.compat.ResponseObserver;
import com.sayweee.wrapper.core.compat.ResponseTransformer;
import com.sayweee.wrapper.core.compat.SimpleObserver;
import com.sayweee.wrapper.helper.lifecycle.LifecycleProvider;
import com.sayweee.wrapper.http.ResponseException;
import com.sayweee.wrapper.http.RetrofitIml;
import com.sayweee.wrapper.http.support.RequestParams;
import com.stripe.android.AlipayAuthenticator;
import com.stripe.android.ApiResultCallback;
import com.stripe.android.PaymentConfiguration;
import com.stripe.android.PaymentIntentResult;
import com.stripe.android.Stripe;
import com.stripe.android.model.ConfirmPaymentIntentParams;
import com.stripe.android.model.PaymentIntent;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;

import io.reactivex.Observable;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Consumer;

/**
 * Author:  winds
 * Date:    2/28/22.
 * Desc:
 */
public class PaymentHelper {

    private static final int LOOP_COUNT = 5;
    private static final String PENDING = "pending";
    private static final String SUCCESS = "success";

    /**
     * alipay 支付，此方案仅针对非conbine pay
     *
     * @param isPayByCitcon 是否为citcon方案支付 true = citcon alipay false = stripe alipay
     * @param isCombinePay  是否为combine checkout pay true = 是，此时orderIds为数组 false = 否，此时orderIds为数组取第一个
     * @param orderIds      订单id
     * @param amount        订单金额
     * @param callback      回调
     */
    public static void prepareAliPay(boolean isPayByCitcon, boolean isCombinePay, @NonNull List<Integer> orderIds, double amount, @NonNull OnPaymentCallback callback) {
        if (orderIds == null || orderIds.isEmpty() || callback == null) {
            return;
        }
        if (isPayByCitcon) {
            CitconPaymentHelper.prepareAliPayByCitcon(isCombinePay, orderIds, amount, callback);
        } else {
            prepareAliPayByStripe(isCombinePay, orderIds, amount, callback);
        }
    }

    //*************************************************************************************************/

    /**
     * alipay by stripe
     **/

    private static void prepareAliPayByStripe(boolean isCombinePay, @NonNull List<Integer> orderIds, double amount, @NonNull OnPaymentCallback callback) {
        OrderApi loader = getLoader();
        if (loader == null || orderIds == null || orderIds.isEmpty()) {
            return;
        }
        Observable<ResponseBean<AliPayBean>> observable;
        RequestParams params = new RequestParams().put("amount", amount).put("currency", "USD").put("version", "V1").put("platform", "app");
        if (isCombinePay) {
            observable = loader.stripeAlipayV2(params.put("order_ids", (Serializable) orderIds).get());
        } else {
            observable = loader.stripeAlipay(params.put("purchase_id", orderIds.get(0)).get());
        }
        observable
                .compose(ResponseTransformer.scheduler())
                .subscribe(new ResponseObserver<ResponseBean<AliPayBean>>() {

                    @Override
                    public void onError(FailureBean failure) {
                        PaymentHelper.dispatchError(callback, failure);
                    }

                    @Override
                    public void onResponse(ResponseBean<AliPayBean> response) {
                        execAliPayByStripe(isCombinePay, orderIds, response.getData(), callback);
                    }
                });
    }

    private static void execAliPayByStripe(boolean isCombinePay, @NonNull List<Integer> orderIds, @NonNull AliPayBean data, @NonNull OnPaymentCallback callback) {
        if (callback == null || data == null) {
            return;
        }
        Activity activity = LifecycleProvider.get().getTopActivity();
        if (data.client_secret == null || activity == null) {
            dispatchResult(callback, false, data.cancel_url);
            return;
        }

        Application application = activity.getApplication();
        Stripe stripe;
        try {
            stripe = new Stripe(application, PaymentConfiguration.getInstance(application).getPublishableKey());
        } catch (Exception ignored) {
            stripe = null;
        }
        if (stripe == null) {
            dispatchResult(callback, false, data.cancel_url);
            return;
        }

        stripe.confirmAlipayPayment(ConfirmPaymentIntentParams.createAlipay(data.client_secret), new AlipayAuthenticator() {
            @Override
            public @NonNull Map<String, String> onAuthenticationRequest(@NonNull String s) {
                return new PayTask(activity).payV2(s, true);
            }
        }, new ApiResultCallback<PaymentIntentResult>() {
            @Override
            public void onSuccess(@NonNull PaymentIntentResult paymentIntentResult) {
                Logger.json(paymentIntentResult);
                PaymentIntent paymentIntent = paymentIntentResult.getIntent();
                PaymentIntent.Status status = paymentIntent.getStatus();
                Logger.json(status);
                prepareCheckOrderStatus(isCombinePay, orderIds, data, callback);
            }

            @Override
            public void onError(@NonNull Exception e) {
                Logger.error(e);
                prepareCheckOrderStatus(isCombinePay, orderIds, data, callback);
            }
        });
    }

    public static void onCitconPayResume() {
        CitconPaymentHelper.onCitconPayResume();
    }

    //*************************************************************************************************/
    /**
     * loop check
     **/
    private static Disposable checkDisposable;

    static void prepareCheckOrderStatus(boolean isCombinePay, @NonNull List<Integer> orderIds, @NonNull PayBean data, @NonNull OnPaymentCallback callback) {
        Observable.interval(0, 1, TimeUnit.SECONDS)
                .take(LOOP_COUNT)
                .doOnSubscribe(new Consumer<Disposable>() {
                    @Override
                    public void accept(Disposable disposable) throws Exception {
                        checkDisposable = disposable;
                    }
                })
                .subscribe(new SimpleObserver<Long>() {
                    @Override
                    public void onNext(@NonNull Long l) {
                        if (isCombinePay) {
                            checkOrderStatusV3(orderIds, data, callback, l.intValue());
                        } else {
                            checkOrderStatus(orderIds != null && orderIds.size() > 0 ? orderIds.get(0) : -1, data, callback, l.intValue());
                        }
                    }
                });
    }

    private static void checkOrderStatus(int orderId, @NonNull PayBean data, @NonNull OnPaymentCallback callback, int flag) {
        getLoader()
                .checkOrderStatus(orderId)
                .compose(ResponseTransformer.scheduler())
                .subscribe(new ResponseObserver<ResponseBean<String>>() {
                    @Override
                    public void onResponse(ResponseBean<String> response) {
                        String result = response.getData();
                        if (PENDING.equalsIgnoreCase(result)) {
                            if (flag >= LOOP_COUNT - 1) {
                                cancelCheck();
                                dispatchResult(callback, false, data.getCancelUrl());
                            }
                        } else if (SUCCESS.equalsIgnoreCase(result)) {
                            cancelCheck();
                            dispatchResult(callback, true, data.getSuccessUrl());
                        } else {
                            onError(new FailureBean(true, "", new ResponseException("")));
                        }
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        if (flag >= LOOP_COUNT - 1) {
                            cancelCheck();
                            dispatchResult(callback, false, data.getCancelUrl());
                        }
                    }
                });
    }

    private static void checkOrderStatusV3(List<Integer> orderIds, PayBean data, @NonNull OnPaymentCallback callback, int flag) {
        Map<String, Serializable> params = new RequestParams().putNonNull("order_ids", (Serializable) orderIds).get();
        getLoader()
                .checkOrderStatusV3(params)
                .compose(ResponseTransformer.scheduler())
                .subscribe(new ResponseObserver<ResponseBean<PayStatusBean>>() {
                    @Override
                    public void onResponse(ResponseBean<PayStatusBean> response) {
                        String result = response.getData().status;
                        if (PENDING.equalsIgnoreCase(result)) {
                            if (flag >= LOOP_COUNT - 1) {
                                cancelCheck();
                                dispatchResult(callback, false, data.getCancelUrl());
                            }
                        } else if (SUCCESS.equalsIgnoreCase(result)) {
                            cancelCheck();
                            dispatchResult(callback, true, data.getSuccessUrl());
                        } else {
                            onError(new FailureBean(true, "", new ResponseException("")));
                        }
                    }

                    @Override
                    public void onError(FailureBean failure) {
                        if (flag >= LOOP_COUNT - 1) {
                            cancelCheck();
                            dispatchResult(callback, false, data.getCancelUrl());
                        }
                    }
                });
    }

    private static void cancelCheck() {
        if (checkDisposable != null && !checkDisposable.isDisposed()) {
            checkDisposable.dispose();
            checkDisposable = null;
        }
    }

    static void dispatchResult(OnPaymentCallback callback, boolean success, String url) {
        if (callback != null) {
            callback.onResult(success, url);
        }
    }

    static void dispatchError(OnPaymentCallback callback, FailureBean failure) {
        if (callback instanceof OnPaymentEventCallback) {
            ((OnPaymentEventCallback) callback).onError(failure);
        }
    }

    static OrderApi getLoader() {
        return RetrofitIml.get().getHttpService(OrderApi.class);
    }

    //*************************************************************************************************/

    /**
     * wechat pay by citcon
     **/
    public static void prepareWechatPayByCitcon(boolean isCombinePay, @NonNull List<Integer> orderIds, double amount, @NonNull OnPaymentCallback callback) {
        CitconPaymentHelper.prepareWechatPayByCitcon(isCombinePay, orderIds, amount, callback);
    }

    //*************************************************************************************************/

    /**
     * cash app pay by citcon
     **/
    public static void prepareCashAppPayByCitcon(@NonNull CashAppPayBean bean, @NonNull OnPaymentCallback callback) {
        CitconPaymentHelper.prepareCashAppPayByCitcon(bean, callback);
    }

    public static void prepareCheckCashAppPayByCitcon(
            @NonNull CashAppPayBean bean,
            @NonNull CitconPayBean payBean,
            @NonNull PaymentHelper.OnPaymentCallback callback
    ) {
        CitconPaymentHelper.prepareCheckCashAppPayByCitcon(bean, payBean, callback);
    }

    public static void execPayPalPay(
            @NonNull final PayPalPayBean bean,
            @NonNull final OnPaymentCallback callback
    ) {
        OrderApi api = getLoader();
        if (api == null) {
            dispatchResult(callback, false, bean.getCancelUrl());
            return;
        }

        final boolean isCombinePay = false;// bean.isCombinePay();
        RequestParams requestParams = new RequestParams()
                .putNonNull("order_ids", (Serializable) bean.getOrderIds())
                .putNonNull("amount", bean.getAmount())
                .putNonNull("device_data", bean.getDeviceData())
                .putNonNull("platform", "android")
                .putNonNull("payment_method_nonce", bean.getNonce());
        Observable<SimpleResponseBean> call = api.paypalBraintreePayV2(requestParams.create());
        call.compose(ResponseTransformer.scheduler())
                .subscribe(new ResponseObserver<SimpleResponseBean>() {

                    @Override
                    public void onResponse(SimpleResponseBean response) {
                        // nothing care about response
                        // check order status when finish
                    }

                    @Override
                    public void onFinish() {
                        super.onFinish();
                        prepareCheckOrderStatus(isCombinePay, bean.getOrderIds(), bean, callback);
                    }
                });
    }

    //*************************************************************************************************/

    /**
     * venmo pay by braintree
     **/
    public static void prepareVenmoPay(@NonNull VenmoPayBean bean, @NonNull OnPaymentEventCallback callback) {
        OrderApi api = getLoader();
        if (api == null) {
            dispatchResult(callback, false, bean.getCancelUrl());
            return;
        }

        final boolean isCombinePay = bean.isCombinePay();
        RequestParams requestParams = new RequestParams()
                .putNonNull("amount", bean.getAmount())
                .putNonNull("currency", "USD")
                .putNonNull("device_data", bean.getDeviceData())
                .putNonNull("platform", "android")
                .putNonNull("version", "v1")
                .putNonNull("payment_method_nonce", bean.getNonce());
        Observable<SimpleResponseBean> call;
        if (isCombinePay) {
            requestParams.putNonNull("order_ids", (Serializable) bean.getOrderIds());
            call = api.venmoBraintreePayV2(requestParams.create());
        } else {
            requestParams.putNonNull("purchase_id", CollectionUtils.firstOrNull(bean.getOrderIds()));
            call = api.venmoBraintreePay(requestParams.create());
        }
        call.compose(ResponseTransformer.scheduler())
                .subscribe(new ResponseObserver<SimpleResponseBean>() {

                    @Override
                    public void onResponse(SimpleResponseBean response) {
                        // nothing care about response
                        // check order status when finish
                    }

                    @Override
                    public void onFinish() {
                        super.onFinish();
                        prepareCheckOrderStatus(isCombinePay, bean.getOrderIds(), bean, callback);
                    }
                });
    }

    //*************************************************************************************************/
    /**
     * 加解密
     **/

    private static final String KEY = "sayweee@applepay";

    /**
     * AES 加密
     *
     * @param content 要加密的字符串
     * @return 加密后的字符串
     */
    @SuppressWarnings({"squid:S5542", "squid:S6437"})
    public static String encrypt(String content) {
        try {
            @SuppressLint("GetInstance")
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS7Padding");
            SecretKeySpec key = new SecretKeySpec(KEY.getBytes(), "AES");
            cipher.init(Cipher.ENCRYPT_MODE, key);
            return Hex.bytesToStringUppercase(cipher.doFinal(content.getBytes()));
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * AES 解密
     *
     * @param content 要解密的字符串
     * @return 解密后的字符串
     */
    @SuppressWarnings({"squid:S5542", "squid:S6437"})
    public static String decrypt(String content) {
        try {
            @SuppressLint("GetInstance")
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS7Padding");
            SecretKeySpec key = new SecretKeySpec(KEY.getBytes(), "AES");
            cipher.init(Cipher.DECRYPT_MODE, key);
            return new String(cipher.doFinal(Hex.stringToBytes(content)));
        } catch (Exception e) {
            return null;
        }
    }

    //*************************************************************************************************/

    /**
     * pay result & event callback
     **/

    public interface OnPaymentCallback {
        void onResult(boolean result, String url);
    }

    public interface OnPaymentEventCallback extends OnPaymentCallback {

        void onError(FailureBean failure);

    }

    public interface OnPaymentRedirectCallback extends OnPaymentCallback {
        void onPaymentRedirect(String url, String cancelUrl);
    }

}
