package com.sayweee.weee.service.helper;

import android.app.Activity;
import android.text.TextUtils;

import com.luck.picture.lib.config.PictureConfig;
import com.luck.picture.lib.config.PictureMimeType;
import com.luck.picture.lib.entity.LocalMedia;
import com.luck.picture.lib.language.LanguageConfig;
import com.luck.picture.lib.listener.OnResultCallbackListener;
import com.luck.picture.lib.style.PictureWindowAnimationStyle;
import com.luck.picture.lib.tools.SdkVersionUtils;
import com.sayweee.logger.Logger;
import com.sayweee.weee.R;
import com.sayweee.weee.global.manager.LanguageManager;
import com.sayweee.weee.module.post.edit.PostEditorActivity;
import com.sayweee.weee.module.post.service.GlideEngine;
import com.sayweee.weee.utils.EmptyUtils;

import java.util.List;

import me.winds.picture.WeeePictureConfig;
import me.winds.picture.WeeePictureSelector;

/**
 * Author:  winds
 * Date:    2021/7/21.
 * Desc:
 */
public class PictureSelectorHelper {

    private PictureSelectorHelper() {

    }

    public static void execVideoSelector(Activity activity, String source) {
        if (activity != null) {
            PictureSelectorHelper.showVideoSelector(activity, new OnResultCallbackListener<LocalMedia>() {
                @Override
                public void onResult(List<LocalMedia> list) {
                    if (!EmptyUtils.isEmpty(list)) {
                        LocalMedia media = list.get(0);
                        Logger.json(media);
                        activity.startActivity(PostEditorActivity.getIntentOnAdd(activity, media, null, source));
                    }
                }

                @Override
                public void onCancel() {

                }
            });
        }
    }

    public static void showVideoSelector(Activity activity, OnResultCallbackListener<LocalMedia> listener) {
        WeeePictureConfig.DEFAULT_VIDEO_MAX_DURATION = 5 * 60 * 1000 + 1000;
        WeeePictureSelector.create(activity)
                .openGallery(PictureMimeType.ofVideo())
                .isWeeeStyle(true)
                .isCamera(false)
                .isCompress(false)
                .setLanguage(getPictureLanguageConfig())
                .isPreviewVideo(false)
                .isPreviewImage(false)
                .imageEngine(GlideEngine.createGlideEngine())
                .selectionMode(PictureConfig.SINGLE)
                .setPictureWindowAnimationStyle(PictureWindowAnimationStyle.ofCustomWindowAnimationStyle(R.anim.anim_trans_bottom_in, R.anim.anim_trans_bottom_out))
                .forResult(listener);
    }

    public static void showPictureSelector(Activity activity, int count, boolean showCamera, OnResultCallbackListener<LocalMedia> listener) {
        showPictureSelector(activity, count, showCamera, null, listener);
    }

    public static void showPictureSelector(
            Activity activity,
            int count,
            boolean showCamera,
            List<LocalMedia> selectionData,
            OnResultCallbackListener<LocalMedia> listener
    ) {
        WeeePictureSelector.create(activity)
                .openGallery(PictureMimeType.ofImage())
                .isWeeeEnkiStyle(true)
                .isCompress(true)
                .setLanguage(getPictureLanguageConfig())
                .isPreviewVideo(false)
                .isPreviewImage(false)
                .maxSelectNum(count)
                .selectionData(selectionData)
                .isCamera(showCamera)
                .imageEngine(GlideEngine.createGlideEngine())
                .setPictureWindowAnimationStyle(PictureWindowAnimationStyle.ofCustomWindowAnimationStyle(R.anim.anim_trans_bottom_in, R.anim.anim_trans_bottom_out))
                .forResult(listener);
    }

    public static void showPictureSelectorOnlyCamera(Activity activity, OnResultCallbackListener<LocalMedia> listener) {
        WeeePictureSelector.create(activity)
                .openCamera(PictureMimeType.ofImage())
                .isWeeeStyle(true)
                .isCompress(true)
                .maxSelectNum(1)
                .setLanguage(getPictureLanguageConfig())
                .imageEngine(GlideEngine.createGlideEngine())
                .setPictureWindowAnimationStyle(PictureWindowAnimationStyle.ofCustomWindowAnimationStyle(R.anim.anim_trans_bottom_in, R.anim.anim_trans_bottom_out))
                .forResult(listener);
    }

    public static String getMediaCompatPath(LocalMedia media) {
        String path = null;
        if (media.isCut()) {
            path = media.getCutPath();
        } else if (media.isCompressed()) {
            path = media.getCompressPath();
        }
        if (TextUtils.isEmpty(path) && SdkVersionUtils.checkedAndroid_Q()) {
            path = media.getPath();
        }
        /*if (CommonTools.isHarmonyOs()) {
            File file = new File(path);
            if (file.exists() && file.isFile()) {
                if (file.length() <= 0) {
                    path = media.getRealPath();
                }
            }
        }*/
        if (TextUtils.isEmpty(path)) {
            path = media.getRealPath();
        }
        return path;
    }

    private static int getPictureLanguageConfig() {
        String language = LanguageManager.get().getLanguage();
        if (language != null) {
            switch (language) {
                case LanguageManager.Language.CHINESE:
                case LanguageManager.Language.CHINESE_OLD:
                    return LanguageConfig.CHINESE;
                case LanguageManager.Language.TRADITIONAL_CHINESE:
                    return LanguageConfig.TRADITIONAL_CHINESE;
                case LanguageManager.Language.KOREAN:
                    return LanguageConfig.KOREA;
                case LanguageManager.Language.JAPANESE:
                    return LanguageConfig.JAPAN;
                case LanguageManager.Language.ENGLISH:
                    return LanguageConfig.ENGLISH;
                case LanguageManager.Language.SPANISH:
                    return LanguageConfig.SPANISH;
                case LanguageManager.Language.VIETNAMESE:
                    return LanguageConfig.VIETNAM;
                case LanguageManager.Language.PORTUGUESE:
                    return LanguageConfig.PORTUGAL;
                default:
                    return LanguageConfig.ENGLISH;
            }
        }
        return LanguageConfig.ENGLISH;
    }
}
