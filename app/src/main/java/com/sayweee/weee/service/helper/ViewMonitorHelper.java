package com.sayweee.weee.service.helper;

import android.os.SystemClock;

import com.sayweee.logger.Logger;

import java.util.HashMap;
import java.util.Map;

public final class ViewMonitorHelper {

    private static final boolean ENABLE = false;

    private static final Map<String, Long> STORAGE = new HashMap<>();

    private ViewMonitorHelper() {

    }

    public static void clear() {
        STORAGE.clear();
    }

    public static void start(String key) {
        if (ENABLE) {
            STORAGE.put(key, SystemClock.uptimeMillis());
        }
    }

    public static void end(String key) {
        if (ENABLE) {
            Long start = STORAGE.get(key);
            if (start != null && start > 0) {
                long end = SystemClock.uptimeMillis();
                long diff = end - start;
                if (diff > 16) {
                    Logger.f("monitor").i("monitor==>abnormal", key + " = " + diff);
                } else {
                    Logger.f("monitor").i("monitor==>", key + " = " + diff);
                }
                STORAGE.put(key, 0L);
            }
        }
    }
}
