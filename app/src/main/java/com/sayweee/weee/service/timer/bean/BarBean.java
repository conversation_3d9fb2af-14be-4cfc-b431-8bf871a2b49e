package com.sayweee.weee.service.timer.bean;

import com.sayweee.weee.utils.EmptyUtils;

import java.io.Serializable;
import java.util.List;

public class BarBean implements Serializable {

    public List<BarObject> object;

    public static class BarObject implements Serializable {
        /**
         * pos : top
         * priority : 1
         * color : #ccc
         * bg_color : #ccc
         * bg_img :
         * link :
         * icon_url :
         * show_time : 10
         * title : {"text":"文本 text","sub_text":"文本 sub_text"}
         * countdown : {"title":"countdown 文本 title","type":2,"end_time":1112323,"number_color":"#22ffaa","number_bg_color":"#22ffaa"}
         * right_cta : {"title":"View More","type":1,"link":""}
         * close_cta : {"visible":true,"icon_url":"","link":""}
         * pages : ["page_home","page_category","page_cart","page_me","page_search","page_product_details","page_bogo_collect"]
         */

        public String pos;
        public int priority;
        public String color;
        public String bg_color;
        public String bg_img;
        public String link;
        public String icon_url;
        public int show_time;
        public Title title;
        public Countdown countdown;
        public RightCta right_cta;
        public CloseCta close_cta;
        public List<String> pages;

        public static class Title implements Serializable {
            /**
             * text : 文本 text
             * sub_text : 文本 sub_text
             */

            public String text;
            public String sub_text;
        }

        public static class Countdown implements Serializable {
            /**
             * title : countdown 文本 title
             * type : 2
             * end_time : 1112323
             * number_color : #22ffaa
             * number_bg_color : #22ffaa
             */

            public String title;
            public int type;
            public int end_time;
            public String number_color;
            public String number_bg_color;
        }

        public static class RightCta implements Serializable {
            /**
             * title : View More
             * type : 1
             * link :
             */

            public String title;
            public int type;
            public String link;
        }

        public static class CloseCta implements Serializable {
            /**
             * visible : true
             * icon_url :
             * link :
             */

            public boolean visible;
            public String icon_url;
            public String link;
        }
    }

    public BarObject getPromotionBar() {
        return !EmptyUtils.isEmpty(object) ? object.get(0) : null;
    }

    public boolean isShowIcon() {
        return !EmptyUtils.isEmpty(getPromotionBar().icon_url);
    }

    public boolean isShowTitle() {
        return getPromotionBar().title != null && !EmptyUtils.isEmpty(getPromotionBar().title.text);
    }

    public boolean isShowSubTitle() {
        return getPromotionBar().title != null && !EmptyUtils.isEmpty(getPromotionBar().title.sub_text);
    }

    public boolean isShowCountDown() {
        return getPromotionBar().countdown != null;
    }

    public boolean isShowCloseCta() {
        return getPromotionBar().close_cta != null && getPromotionBar().close_cta.visible;
    }
}
