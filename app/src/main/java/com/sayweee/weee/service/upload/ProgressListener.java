package com.sayweee.weee.service.upload;

import androidx.annotation.NonNull;

public abstract class ProgressListener implements ProgressCallback {

    private static final int INTERVAL = 100;

    private final long id;
    private boolean started;
    private long lastRefreshTime = 0L;
    private long lastBytesWritten = 0L;

    public ProgressListener(long id) {
        this.id = id;
    }

    public final void onProgressChanged(long numBytes, long totalBytes, float percent) {
        if (!started) {
            onProgressStart(totalBytes);
            started = true;
        }
        if (numBytes == -1 && totalBytes == -1 && percent == -1) {
            onProgressChanged(new ProgressInfo(id));
            return;
        }
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastRefreshTime >= INTERVAL || numBytes == totalBytes || percent >= 1F) {
            long intervalTime = (currentTime - lastRefreshTime);
            if (intervalTime == 0) {
                intervalTime += 1;
            }
            long updateBytes = numBytes - lastBytesWritten;
            final long networkSpeed = updateBytes / intervalTime;
            onProgressChanged(new ProgressInfo(id, numBytes, totalBytes, percent, networkSpeed));
            lastRefreshTime = System.currentTimeMillis();
            lastBytesWritten = numBytes;
        }
        if (numBytes == totalBytes || percent >= 1F) {
            onProgressFinish();
        }
    }

    public abstract void onProgressChanged(@NonNull ProgressInfo progressInfo);

    public void onProgressStart(long totalBytes) {

    }

    public void onProgressFinish() {

    }
}