package com.sayweee.weee.service.helper;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.sayweee.service.ContextService;
import com.sayweee.service.SessionService;
import com.sayweee.service.core.IServiceBridge;
import com.sayweee.weee.BuildConfig;
import com.sayweee.weee.global.config.DevConfig;
import com.sayweee.weee.global.manager.LanguageManager;
import com.sayweee.wrapper.helper.lifecycle.LifecycleProvider;

public class ServiceBridgeImpl implements IServiceBridge {

    @NonNull
    @Override
    public String getVersionName() {
        return BuildConfig.VERSION_NAME;
    }

    @Override
    public int getVersionCode() {
        return BuildConfig.VERSION_CODE;
    }

    @NonNull
    @Override
    public String getLanguage() {
        return LanguageManager.get().getLanguage();
    }

    @NonNull
    @Override
    public String getDeviceId() {
        return ContextService.get().getDeviceId(LifecycleProvider.get().getApplication());
    }

    @Nullable
    @Override
    public String getUserAgent() {
        return SessionService.get().getUserAgent();
    }

    @NonNull
    @Override
    public String getBuildFlavor() {
        return DevConfig.FLAVOR;
    }
}
