package com.sayweee.weee.service.helper;

import android.app.Activity;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.GradientDrawable;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.collection.ArrayMap;
import androidx.core.content.ContextCompat;

import com.sayweee.weee.R;
import com.sayweee.weee.databinding.LayoutProgressBarUpsellBinding;
import com.sayweee.weee.global.manager.ImageLoader;
import com.sayweee.weee.global.manager.OrderManager;
import com.sayweee.weee.module.cart.bean.UpdateResultBean;
import com.sayweee.weee.module.cart.service.PantryHelper;
import com.sayweee.weee.module.seller.SellerActivity;
import com.sayweee.weee.module.seller.SellerPageParams;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackModel;
import com.sayweee.weee.service.analytics.bean.EagleContext;
import com.sayweee.weee.service.track.TrackParams;
import com.sayweee.weee.service.track.WeeeEvent;
import com.sayweee.weee.service.webp.ImageSpec;
import com.sayweee.weee.service.webp.WebpManager;
import com.sayweee.weee.utils.BitmapUtils;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.widget.HorizontalProgressBar;
import com.sayweee.weee.widget.HtmlTextView;
import com.sayweee.weee.widget.ProgressBarContainer;
import com.sayweee.weee.widget.snackbar.ActionSnackBarView;
import com.sayweee.weee.widget.snackbar.data.ActionSnackBarData;
import com.sayweee.widget.shape.ShapeTextView;
import com.sayweee.widget.toaster.Toaster;
import com.sayweee.widget.toaster.snackbar.SnackBarOptions;
import com.sayweee.wrapper.helper.lifecycle.LifecycleProvider;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Author:  winds
 * Email:   <EMAIL>
 * Date:    2021/4/21.
 * Desc:
 */
public class ProgressBarManager {

    static final ProgressBarManager manager = new ProgressBarManager();

    List<ProgressChangedListener> listeners = new ArrayList<>();

    public static ProgressBarManager get() {
        return manager;
    }

    private ProgressBarManager() {

    }

    public void hideProgressBar(ProgressBarContainer rootView) {
        rootView.showWithAnim(/* show= */false);
        com.sayweee.widget.component.HorizontalProgressBar pbDeal;
        pbDeal = rootView.findViewById(R.id.layout_progress_bar_upsell).findViewById(R.id.progress_indicator);
        pbDeal.setProgress(0);
    }

    public boolean setProgressBar(String pageKey, @NonNull ProgressBarContainer rootView, @Nullable UpdateResultBean beanInfo) {
        if (beanInfo == null) {
            hideProgressBar(rootView);
            return false;
        }
        UpdateResultBean.TagInfoBean tagInfo = beanInfo.tagInfo;
        if(EmptyUtils.isEmpty(tagInfo)){
            hideProgressBar(rootView);
            return false;
        }

        // MKPL progress bar 控制只在Seller home弹出
        if (tagInfo.isSeller() && !(rootView.getContext() instanceof SellerActivity)) {
            return false;
        }

        // Trade in upsell only show in category page
        if (tagInfo.isTradeInUpsell() && !WeeeEvent.PageView.CATEGORY.equals(pageKey)) {
            return false;
        }

        View normalView = rootView.findViewById(R.id.layout_progress_bar_normal);
        View upSellView = rootView.findViewById(R.id.layout_progress_bar_upsell);
        if (tagInfo.isTradeInUpsell()) {
            ViewTools.setViewVisibilityIfChanged(normalView, false);
            ViewTools.setViewVisibilityIfChanged(upSellView, true);
            setProgressBarUpSell(rootView, upSellView, tagInfo);
        } else {
            ViewTools.setViewVisibilityIfChanged(normalView, true);
            ViewTools.setViewVisibilityIfChanged(upSellView, false);
            setProgressBarNormal(rootView, normalView, tagInfo);
        }
        return true;
    }

    private void setProgressBarNormal(@NonNull ProgressBarContainer rootView, @NonNull View view, @NonNull UpdateResultBean.TagInfoBean tagInfo) {
        HtmlTextView progressContent = view.findViewById(R.id.progress_content);
        ShapeTextView progressName = view.findViewById(R.id.progress_name);
        progressContent.setHtmlText(tagInfo.tag);
        progressName.setTextColor(ContextCompat.getColor(view.getContext(), R.color.color_fore));
        ViewTools.applyTextStyle(progressName, R.style.style_body_3xs_strong);
        if (tagInfo.isSeller()) {
            progressName.setPadding(CommonTools.dp2px(8), CommonTools.dp2px(4), CommonTools.dp2px(8), CommonTools.dp2px(4));
            progressName.setText(tagInfo.type);
            progressName.setCompoundDrawables(null, null, null, null);
            progressName.setBackgroundSolidDrawable(Color.parseColor("#CCF2EC"), CommonTools.dp2px(25));
            progressName.setVisibility(TextUtils.isEmpty(tagInfo.type) ? View.GONE : View.VISIBLE);
            progressName.setTextColor(ContextCompat.getColor(view.getContext(), R.color.brand_color_tone_green_key_dark));
        } else if (tagInfo.isPantry()) { //图片
            progressName.setPadding(0, 0, 0, 0);
            progressName.setBackground(null);
            progressName.setCompoundDrawables(PantryHelper.getProductPantryDrawable(view.getContext()), null, null, null);
            progressName.setVisibility(View.VISIBLE);
        } else {
            progressName.setPadding(CommonTools.dp2px(4), CommonTools.dp2px(2), CommonTools.dp2px(4), CommonTools.dp2px(2));
            progressName.setText(tagInfo.type);
            progressName.setCompoundDrawables(null, null, null, null);
            int bgSolidColor = tagInfo.isGrocery() ? ContextCompat.getColor(view.getContext(), R.color.color_tertiary_electric_1) : 0XFF1EBA9C;
            int textColor = tagInfo.isGrocery() ? ContextCompat.getColor(view.getContext(), R.color.color_primary_1) : 0XFFFFFFFF;
            progressName.setBackgroundDrawable(bgSolidColor, CommonTools.dp2px(view.getContext(), R.dimen.prop_size_radius_200, 4), bgSolidColor, 0);
            progressName.setVisibility(TextUtils.isEmpty(tagInfo.type) || OrderManager.get().isMailMode() ? View.GONE : View.VISIBLE);
            progressName.setTextColor(textColor);
        }

        HorizontalProgressBar progressIndicator = view.findViewById(R.id.progress_indicator);
        if (tagInfo.isSeller()) {
            progressIndicator
                    .setBgColor(ContextCompat.getColor(view.getContext(), R.color.brand_color_tone_green_spectrum_2))
                    .setProgressColor(ContextCompat.getColor(view.getContext(), R.color.brand_color_tone_green_key_primary))
                    .setProgressGradientStartColor(ContextCompat.getColor(view.getContext(), R.color.brand_color_tone_green_key_primary));
        } else if (tagInfo.isGrocery() || tagInfo.isPantry()) {
            progressIndicator
                    .setBgColor(ContextCompat.getColor(view.getContext(), R.color.color_surface_200_bg))
                    .setProgressColor(ContextCompat.getColor(view.getContext(), R.color.color_primary_2))
                    .setProgressGradientStartColor(ContextCompat.getColor(view.getContext(), R.color.color_primary_2));
        } else {
            progressIndicator
                    .setBgColor(0xFFBFEFE7)
                    .setProgressColor(0XFF1EBA9C)
                    .setProgressGradientStartColor(0xFF77DBCF);
        }
        int progress = tagInfo.shipping_free_progress != null ? tagInfo.shipping_free_progress : 0;
        progressIndicator.setProgress(progress);

        rootView.setTargetUrl(tagInfo.turn_url);
        rootView.dismiss(3000L);
        rootView.showWithAnim(true);
    }

    private void setProgressBarUpSell(@NonNull ProgressBarContainer rootView, @NonNull View view, @NonNull UpdateResultBean.TagInfoBean tagInfo) {
        Context context = view.getContext();
        boolean isProgressBarHidden = !rootView.isShowing();
        LayoutProgressBarUpsellBinding b = LayoutProgressBarUpsellBinding.bind(view);
        boolean unsatisfied = tagInfo.diff_price > 0;
        if (unsatisfied) { // unreached
            b.getRoot().setBackground(ViewTools.getDrawable(context, R.mipmap.bg_progress_bar_upsell_normal));
        } else {
            b.getRoot().setBackground(ViewTools.getDrawable(context, R.mipmap.bg_progress_bar_upsell_reached));
        }
        if (!EmptyUtils.isEmpty(tagInfo.image_url)) {
            b.ivIcon.setBackground(ViewTools.getDrawable(context, R.color.color_surface_1_bg_idle));
            b.ivIcon.setStrokeWidth(CommonTools.dp2px(1));
            b.ivIcon.setRadius(CommonTools.dp2px(8));
            ImageLoader.load(context, b.ivIcon, WebpManager.convert(ImageSpec.SPEC_PRODUCT, tagInfo.image_url));
        } else {
            b.ivIcon.setBackground(null);
            b.ivIcon.setStrokeWidth(0);
            b.ivIcon.setRadius(0);
            ImageLoader.load(context, b.ivIcon, R.mipmap.ic_upsell_bar_default);
        }
        ViewTools.setViewHtml(b.progressTitle, tagInfo.tag_desc);
        b.progressTitle.setCompoundDrawables(null, null, null, null);
        if (!unsatisfied) {
            Drawable leftDrawable = ViewTools.getDrawableWithBounds(
                    context, R.mipmap.ic_checkmark_circle_472d7b_20x20, CommonTools.dp2px(15), CommonTools.dp2px(15)
            );
            if (leftDrawable != null) {
                b.progressTitle.setCompoundDrawables(leftDrawable, null, null, null);
            }
        }
        ViewTools.setViewHtml(b.progressSubtitle, tagInfo.tag);
        int progress = tagInfo.shipping_free_progress != null ? tagInfo.shipping_free_progress : 0;
        b.progressIndicator.setProgress(progress, true, isProgressBarHidden ? 300L : 0L);
        rootView.showWithAnim(true);
    }

    public interface ProgressChangedListener {
        void onProgressChange(int productId, @Nullable String tagType, @Nullable UpdateResultBean tagInfo);
    }

    public void registerProgressChangedListener(ProgressChangedListener listener) {
        if (listener != null && listeners != null && !listeners.contains(listener)) {
            listeners.add(listener);
        }
    }

    public void unregisterProgressChangedListener(ProgressChangedListener listener) {
        if (listeners != null && listener != null) {
            listeners.remove(listener);
        }
    }

    public void notify(int productId, @Nullable String tagType, @Nullable UpdateResultBean tagInfo) {
        if (listeners != null) {
            for (ProgressChangedListener listener : listeners) {
                if (listener != null) {
                    listener.onProgressChange(productId, tagType, tagInfo);
                }
            }
        }
    }

    public boolean isFirstAddSellerItemNew(String tagType) {
        return "first_add_seller_item_new".equalsIgnoreCase(tagType);
    }

    public boolean isSellerItemExistNew(String tagType) {
        return "seller_item_exist_new".equalsIgnoreCase(tagType);
    }

    public void showSellerItemExistNew(View rootView, int productId, String tagType, UpdateResultBean.TagInfoBean tagInfo) {
        showSellerItemExistNew(rootView, productId, tagType, tagInfo, CommonTools.dp2px(86));
    }

    public void showSellerItemExistNew(View rootView, int productId, String tagType, UpdateResultBean.TagInfoBean tagInfo, int marginBottom) {
        if (rootView == null) {
            return;
        }
        Context context = rootView.getContext();
        String title = String.format(ContextCompat.getString(context, R.string.s_added_to), tagInfo.vendor_name);
        boolean goVendor = tagInfo.shipping_free_progress != null && tagInfo.shipping_free_progress < 100;
        String actionTitle = goVendor ? ContextCompat.getString(context, R.string.s_mkpl_store) : ContextCompat.getString(context, R.string.s_cart);
        ActionSnackBarData.Builder dataBuilder = new ActionSnackBarData.Builder()
                .setIconUrl(WebpManager.convert(ImageSpec.SPEC_PRODUCT, tagInfo.product_image_url))
                .setTitle(title)
                .setSubTitle(ViewTools.fromHtml(tagInfo.tag))
                .setActionTitle(actionTitle);
        ActionSnackBarView snackBarView = new ActionSnackBarView(context);
        snackBarView.convert(dataBuilder.build());
        ViewTools.setViewVisible(snackBarView.findViewById(R.id.tv_action), true);
        ViewTools.setViewVisible(snackBarView.findViewById(R.id.iv_action), true);
        if (tagInfo.shipping_free_progress != null && tagInfo.shipping_free_progress == 100) {
            ViewTools.setViewVisible(snackBarView.findViewById(R.id.iv_action), false);
            TextView tvAction = snackBarView.findViewById(R.id.tv_action);
            //背景
            GradientDrawable drawable = new GradientDrawable();
            drawable.setShape(GradientDrawable.RECTANGLE);
            drawable.setCornerRadius(CommonTools.dp2px(20));
            drawable.setColor(ContextCompat.getColor(context, R.color.color_primary_flow_teal));
            tvAction.setBackground(drawable);
            tvAction.setGravity(Gravity.CENTER_VERTICAL);
            tvAction.setPadding(CommonTools.dp2px(12), 0, CommonTools.dp2px(4), 0);
            tvAction.getLayoutParams().height = CommonTools.dp2px(28);
            //箭头
            Drawable end = BitmapUtils.tint(context, R.mipmap.iccmpt_carat_right_20x20, Color.WHITE);
            end.setBounds(0, 0, CommonTools.dp2px(20), CommonTools.dp2px(20));
            tvAction.setCompoundDrawables(null, null, end, null);
            tvAction.setCompoundDrawablePadding(CommonTools.dp2px(4));
            ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) tvAction.getLayoutParams();
            layoutParams.rightMargin = CommonTools.dp2px(10);
            tvAction.setLayoutParams(layoutParams);
        }
        SellerPageParams pageParams = new SellerPageParams();
        pageParams.openCart = 1;
        String modNm = "mkpl_shipping_tip_popup";
        Map<String, Object> ctx = new EagleContext()
                .setPageTarget(String.valueOf(productId))
                .asMap();
        ctx.put("popup_nm", tagType);
        ViewTools.setViewOnSafeClickListener(snackBarView.findViewById(R.id.layout_root), v -> {
            AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                    .setMod_nm(modNm)
                    .setTargetPos(0)
                    .setClickType(EagleTrackEvent.ClickType.VIEW)
                    .setTargetNm(String.valueOf(tagInfo.vendor_id))
                    .setTargetType(goVendor ? "mkpl_seller" : "cart")
                    .addCtx(ctx)
                    .build().getParams());
            context.startActivity(SellerActivity.getIntentByUrl(context, String.valueOf(tagInfo.vendor_id), goVendor ? null : pageParams, null));
        });
        Activity activity = LifecycleProvider.get().getTopActivity();
        if (activity != null) {
            Toaster.asSnackBar(rootView)
                    .setView(snackBarView)
                    .setOptions(new SnackBarOptions.Builder()
                            .duration(TimeUnit.SECONDS.toMillis(3))
                            .contentMarginBottom(marginBottom)
                            .contentMarginLeft(CommonTools.dp2px(20))
                            .contentMarginRight(CommonTools.dp2px(20))
                            .build())
                    .build()
                    .show(activity);
        }
        //t2_popup
        ArrayMap<String, Object> map = new ArrayMap<>();
        map.put("product_id", productId);
        map.put("shipping_free_progress", tagInfo.shipping_free_progress);
        AppAnalytics.logEvent(EagleTrackEvent.EventType.POPUP_IMP, new EagleTrackModel.Builder()
                .setMod_nm(modNm)
                .addContent(new TrackParams()
                        .put("action", "view")
                        .put("id", tagInfo.vendor_id)
                        .put("name", tagType)
                        .put("other_parameter", map)
                        .get())
                .build()
                .getParams());
    }
}
