package com.sayweee.weee.service.rx;

import io.reactivex.Observable;
import io.reactivex.ObservableSource;
import io.reactivex.ObservableTransformer;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Consumer;
import io.reactivex.schedulers.Schedulers;

/**
 * Author:  winds
 * Email:   <EMAIL>
 * Date:    2020/9/26.
 * Desc:
 */
public class DisposableTransformer<T> implements ObservableTransformer<T, T> {

    private boolean flag;
    private Consumer<? super Disposable> consumer;

    public static <T> DisposableTransformer<T> scheduler() {
        return scheduler(null);
    }

    public static <T> DisposableTransformer<T> scheduler(boolean observeOnMainThread) {
        return new DisposableTransformer<T>(null, observeOnMainThread);
    }

    public static <T> DisposableTransformer<T> scheduler(Consumer<? super Disposable> consumer) {
        return new DisposableTransformer<T>(consumer, true);
    }

    public static <T> DisposableTransformer<T> scheduler(Consumer<? super Disposable> consumer, boolean observeOnMainThread) {
        return new DisposableTransformer<T>(consumer, observeOnMainThread);
    }

    public DisposableTransformer() {
        this(true);
    }

    public DisposableTransformer(boolean observeOnMainThread) {
        this(null, observeOnMainThread);
    }

    public DisposableTransformer(Consumer<? super Disposable> consumer, boolean observeOnMainThread) {
        this.flag = observeOnMainThread;
        this.consumer = consumer;
    }

    @Override
    public ObservableSource<T> apply(Observable<T> upstream) {
        if (consumer != null) {
            upstream = upstream.doOnSubscribe(consumer);
        }
        return upstream
                .subscribeOn(Schedulers.io())
                .unsubscribeOn(Schedulers.io())
                .observeOn(flag ? AndroidSchedulers.mainThread() : Schedulers.io());
    }
}
