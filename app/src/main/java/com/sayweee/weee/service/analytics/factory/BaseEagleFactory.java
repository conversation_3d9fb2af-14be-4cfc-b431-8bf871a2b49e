package com.sayweee.weee.service.analytics.factory;

import androidx.annotation.Nullable;
import androidx.collection.ArrayMap;

import com.alibaba.fastjson.TypeReference;
import com.sayweee.weee.module.cart.bean.CartItemBean;
import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.JsonUtils;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * Author:  winds
 * Date:    3/23/22.
 * Desc:
 */
public abstract class BaseEagleFactory implements IEagleFactory {

    protected final HashMap<String, Object> result = new HashMap<>();

    @Override
    public IEagleFactory setTarget(ProductBean target, int position) {
        return setContent(createContent(target, position));
    }

    @Override
    public IEagleFactory setCartTarget(CartItemBean target, int position) {
        return setContent(createCartContent(target, position));
    }

    @Nullable
    protected abstract Map<String, Object> createContent(ProductBean target, int position);

    @Nullable
    protected Map<String, Object> createCartContent(CartItemBean target, int position) {
        return null;
    }

    protected IEagleFactory setContent(Map<String, Object> content) {
        if (!EmptyUtils.isEmpty(content)) {
            result.put("co", content);
        }
        return this;
    }

    @Override
    public IEagleFactory setElement(Map<String, Object> element) {
        if (element != null) {
            result.putAll(element);
        }
        return this;
    }

    @Override
    public IEagleFactory setContext(Map<String, Object> context) {
        if (!EmptyUtils.isEmpty(context)) {
            result.put("ctx", context);
        }
        return this;
    }

    @Override
    public Map<String, Object> get() {
        return result;
    }

    public Map<String, Object> getDefaultProductContent() {
        Map<String, Object> content = new ArrayMap<>();
        content.put("prod_pos", 0);
        content.put("prod_id", null);
        content.put("recommendation_trace_id", null);
        content.put("prod_name", null);
        content.put("is_pantry", false);
        content.put("is_mkpl", false);
        content.put("label_list", null);
        content.put("price", null);
        content.put("base_price", null);
        content.put("activity_tag_list", null);
        content.put("sold_count_ui", null);
        content.put("restock_tip", null);
        content.put("sold_status", null);
        content.put("qty_in_cart", null);
        content.put("discount_percentage", null);
        content.put("is_limit_product", false);
        content.put("policy_pop_config_key", null);
        content.put("progress", null);
        return content;
    }

    public void putExtraProductContent(ProductBean target, Map<String, Object> content) {
        Map<String, Serializable> extra = JsonUtils.parseObject(target.extra_attrs, new TypeReference<Map<String, Serializable>>() {
        });
        content.put("extra_attrs", extra);
    }
}
