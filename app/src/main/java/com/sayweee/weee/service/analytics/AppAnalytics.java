package com.sayweee.weee.service.analytics;

import android.app.Activity;
import android.app.Application;
import android.os.Build;
import android.webkit.WebView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.collection.ArrayMap;
import androidx.fragment.app.Fragment;

import com.sayweee.analytics.WeeeAnalytics;
import com.sayweee.analytics.config.AnalyticsConfig;
import com.sayweee.service.ContextService;
import com.sayweee.weee.global.config.AppConfig;
import com.sayweee.weee.global.config.VariantConfig;
import com.sayweee.weee.service.helper.SearchTrackInterceptor;
import com.sayweee.weee.service.track.WeeeEvent;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Author:  winds
 * Date:    12/20/21.
 * Desc:
 */
public class AppAnalytics {

    private AppAnalytics() {

    }

    public static void init(Application application) {
        WeeeAnalytics.get().init(application)
                .setAnalyticsParams(new AnalyticsParamsIml())
                .setAnalyticsConfig(new AnalyticsConfig
                        .Builder()
                        .setOsVersion(Build.VERSION.RELEASE + "(" + Build.VERSION.SDK_INT + ")")
                        .setAppVersion(AppConfig.VERSION)
                        .setDeviceId(ContextService.get().getDeviceId(application))
                        .setAppBu(VariantConfig.APP_BU)
                        .setLogEnable(true)
                        .setEnableAutoLogChildPage(false, true)
                        .build())
                .setPageMappingData(getEventMappingData())
                .setPageIgnoredData(getEventIgnoredData());
    }

    /**
     * 设置事件映射，用于pv埋点自动采集，若手动上报，请勿在此设置映射
     *
     * @return map
     */
    private static Map<String, String> getEventMappingData() {
        Map<String, String> mappingData = new ArrayMap<>();
        //登陆
        addMappingEvent(mappingData, "LoginPanelActivity", WeeeEvent.PageView.LOGIN_SIGN_UP);
        addMappingEvent(mappingData, "LoginActivity", WeeeEvent.PageView.LOGIN_SIGN_UP);
        //注册
        addMappingEvent(mappingData, "RegisterPanelActivity", WeeeEvent.PageView.LOGIN_SIGN_UP);
        addMappingEvent(mappingData, "CartActivity", WeeeEvent.PageView.CART);
//        addMappingEvent(mappingData, "CheckOutActivity", WeeeEvent.PageView.CHECKOUT);//需带参数，故手动上报
        addMappingEvent(mappingData, "CouponActivity", WeeeEvent.PageView.COUPON);
        addMappingEvent(mappingData, "OrderProductsActivity", WeeeEvent.PageView.CHECKOUT_PRODUCT_LIST);
        addMappingEvent(mappingData, "PaymentMethodActivity", WeeeEvent.PageView.PAYMENT_METHOD);
        addMappingEvent(mappingData, "PmEditActivity", WeeeEvent.PageView.PAYMENT_METHOD);
        addMappingEvent(mappingData, "DeliveryAddressPickerActivity", WeeeEvent.PageView.ADDRESS);
        addMappingEvent(mappingData, "UpSellActivity", WeeeEvent.PageView.BEFORE_YOU_CHECKOUT);
        addMappingEvent(mappingData, "SectionUpsellActivity", WeeeEvent.PageView.BEFORE_YOU_CHECKOUT);
        addMappingEvent(mappingData, "Ready2CheckoutActivity", WeeeEvent.PageView.CART_SELECTION);
        //addMappingEvent(mappingData, "PostVideoDetailActivity", WeeeEvent.PageView.COMM_VIDEO);//video页面需附带CTX参数，故手动上报
        addMappingEvent(mappingData, "PostEditorActivity", WeeeEvent.PageView.COMM_VIDEO_EDIT);
        addMappingEvent(mappingData, "DateActivity", WeeeEvent.PageView.DELIVERY_DATE_SELECTION);
        addMappingEvent(mappingData, "MessagePortalActivity", WeeeEvent.PageView.ME_MESSAGE_CENTER);
        addMappingEvent(mappingData, "MessageCenterActivity", WeeeEvent.PageView.COMMUNITY_ACTIVITY);
        addMappingEvent(mappingData, "ActivityCenterActivity", WeeeEvent.PageView.ACTIVITY_CENTER);
        addMappingEvent(mappingData, "DeliveryAddressEditActivity", WeeeEvent.PageView.NEW_ADDRESS);
        addMappingEvent(mappingData, "GuideActivity", WeeeEvent.PageView.ONBOARDING);
        addMappingEvent(mappingData, "NewGuideActivity", WeeeEvent.PageView.ONBOARDING);
        addMappingEvent(mappingData, "CreditCardAddActivity", WeeeEvent.PageView.PAYMENT_NEW_CARD);
        addMappingEvent(mappingData, "StripeCardAddActivity", WeeeEvent.PageView.PAYMENT_NEW_CARD);
        addMappingEvent(mappingData, "BraintreeCreditCardAddActivity", WeeeEvent.PageView.PAYMENT_NEW_CARD);
        addMappingEvent(mappingData, "BraintreeCardAddActivity", WeeeEvent.PageView.PAYMENT_NEW_CARD);
        //addMappingEvent(mappingData, "ProductDetailActivity", WeeeEvent.PageView.PRODUCT);//PDP页面需附带CTX参数，故手动上报
        addMappingEvent(mappingData, "PostListActivity", WeeeEvent.PageView.PRODUCT_REVIEW_LIST);
        addMappingEvent(mappingData, "StudentRuleActivity", WeeeEvent.PageView.LOGIN_STUDENT_PREVERIFY);
        addMappingEvent(mappingData, "StudentVerifyActivity", WeeeEvent.PageView.LOGIN_STUDENT_VERIFY);
        //addMappingEvent(mappingData, "BrandActivity", WeeeEvent.PageView.BRAND);
        addMappingEvent(mappingData, "ToReviewHostActivity", WeeeEvent.PageView.POST_REVIEW);
        addMappingEvent(mappingData, "ReviewEditActivity", WeeeEvent.PageView.ME_REVIEW_EDIT);
        addMappingEvent(mappingData, "ReviewSubmittedActivity", WeeeEvent.PageView.POST_REVIEW_SUBMIT);
        addMappingEvent(mappingData, "PostExploreActivity", WeeeEvent.PageView.MKPL_EXPLORE_VIDEO);
        addMappingEvent(mappingData, "RecommendItemsActivity", WeeeEvent.PageView.PROMOTION_RECOMMEND);
        addMappingEvent(mappingData, "EbtCardAddActivity", WeeeEvent.PageView.EBT_CARD_ADD);
        //addMappingEvent(mappingData, "CollectionAutoActivity", WeeeEvent.PageView.AUTO_COLLECTION);
        return mappingData;
    }

    private static void addMappingEvent(Map<String, String> eventMap, @NonNull String clazzName, String eventName) {
        eventMap.put(clazzName, eventName);
    }

    /**
     * 设置忽略pv事件的class，避免干扰事件流
     * 设置后，不会主动对此class做pv埋点
     *
     * @return
     */
    private static List<String> getEventIgnoredData() {
        List<String> ignoredData = new ArrayList<>();
        // 以下 page 已设置手动上报
        ignoredData.add("SplashActivity");
        ignoredData.add("MainActivity");
        ignoredData.add("NewProductDetailActivity");
        ignoredData.add("SearchPanelActivity");
        ignoredData.add("CateActivity");

        // Checkout
        ignoredData.add("CheckoutSectionRouteActivity");
        // Payment
        ignoredData.add("PayPalPaySetupActivity");
        ignoredData.add("VenmoPaySetupActivity");
        ignoredData.add("CashAppPaySetupActivity");
        ignoredData.add("CashAppPaySetupActivity");
        ignoredData.add("PayPaymentActivity");

        // Marketplace
        ignoredData.add("GlobalActivity");
        ignoredData.add("GlobalPlusActivity");
        ignoredData.add("SellerActivity");
        ignoredData.add("SellerInfoActivity");

        return ignoredData;
    }

    public static void logPageView(Activity activity) {
        if (activity != null) {
            WeeeAnalytics.get().logPageView(activity);
        }
    }

    @SuppressWarnings("auto log fragment must set mappding and enable")
    private static void logPageView(Fragment fragment) {
        if (fragment != null) {
            WeeeAnalytics.get().logPageView(fragment);
        }
    }

    public static void logPageView(@NonNull String pageName, Activity target) {
        logPageView(pageName, target, null);
    }

    public static void logPageView(@NonNull String pageName, Fragment target) {
        logPageView(pageName, target, null);
    }

    public static void logPageView(@NonNull String pageName, @NonNull Object pageTarget, @Nullable Map<String, Object> pageContext) {
        if (pageTarget != null) {
            WeeeAnalytics.get().logPageView(pageName, pageTarget.getClass().getSimpleName(), pageTarget, pageContext);
        }
    }

    public static void logPageView(@NonNull String pageName, @NonNull Object pageTarget, @Nullable Map<String, Object> pageContext, @Nullable Map<String, Object> pageParams) {
        if (pageTarget != null) {
            WeeeAnalytics.get().logPageView(pageName, pageTarget.getClass().getSimpleName(), pageTarget, pageContext, pageParams);
        }
    }

    public static void logPageView(@NonNull String pageName, @NonNull Object pageTarget, @NonNull String viewId, @Nullable Map<String, Object> pageContext, @Nullable Map<String, Object> pageParams) {
        if (pageTarget != null) {
            WeeeAnalytics.get().logPageView(pageName, pageTarget.getClass().getSimpleName(), pageTarget, viewId, pageContext, pageParams);
        }
    }

    /**
     * pv添加ctx参数，参数为page_target
     *
     * @param pageParams
     * @param pageTarget
     * @return
     */
    public static Map<String, Object> putPvPageTarget(Map<String, Object> pageParams, String pageTarget) {
        ArrayMap<String, Object> params = new ArrayMap<>();
        params.put("page_target", pageTarget);
        if (pageParams == null) {
            pageParams = new ArrayMap<>();
        }
        pageParams.put("ctx", params);
        return pageParams;
    }

    public static void refreshPageView(Activity activity) {
        if (activity != null) {
            WeeeAnalytics.get().refreshPageView(activity);
        }
    }

    @SuppressWarnings("auto refresh fragment must set mappding and enable")
    private static void refreshPageView(Fragment fragment) {
        if (fragment != null) {
            WeeeAnalytics.get().refreshPageView(fragment);
        }
    }

    public static void refreshPageView(@NonNull String pageName, Activity target) {
        refreshPageView(pageName, target, null);
    }

    public static void refreshPageView(@NonNull String pageName, Fragment target) {
        refreshPageView(pageName, target, null);
    }

    public static void refreshPageView(@NonNull String pageName, @Nullable Object pageTarget, @Nullable Map<String, Object> params) {
        if (pageTarget != null) {
            WeeeAnalytics.get().refreshPageView(pageName, pageTarget.getClass().getSimpleName(), pageTarget, params);
        }
    }

    public static boolean handleAnalyticsJs(WebView view, String url) {
        return WeeeAnalytics.get().handleAnalyticsJs(view, url);
    }

    public static void logEvent(@NonNull String eventName) {
        logEvent(eventName, null);
    }

    /**
     * 埋点，注意此方法不兼容pv埋点
     *
     * @param eventName 事件名
     * @param params    事件参数
     */
    public static void logEvent(@NonNull String eventName, @Nullable Map<String, Object> params) {
        WeeeAnalytics.get().logEvent(eventName, params);
        if (SearchTrackInterceptor.TRACK_EVENT_DEBUG_CONSOLE || SearchTrackInterceptor.TRACK_EVENT_DEBUG_CALL_INTERCEPT || SearchTrackInterceptor.TRACK_EVENT_DEBUG_REQUEST_INTERCEPT) {
            SearchTrackInterceptor.logEventMessage(eventName, params);
        }
    }

    public static void logBannerImp(@Nullable Map<String, Object> params) {
        logEvent(EagleTrackEvent.EventType.BANNER_IMP, params);
    }

    public static void logCartAction(@Nullable Map<String, Object> params) {
        logEvent(EagleTrackEvent.EventType.CART_ACTION, params);
    }

    public static void logEllipseImp(@Nullable Map<String, Object> params) {
        logEvent(EagleTrackEvent.EventType.ELLIPSE_IMP, params);
    }

    public static void logFilterButtonImp(@Nullable Map<String, Object> params) {
        logEvent(EagleTrackEvent.EventType.FILTER_BUTTON_IMP, params);
    }

    public static void logProdImp(@Nullable Map<String, Object> params) {
        logEvent(EagleTrackEvent.EventType.PROD_IMP, params);
    }

    public static void logPageSecImp(@Nullable Map<String, Object> params) {
        logEvent(EagleTrackEvent.EventType.PAGE_SEC_IMP, params);
    }

    public static void logClickAction(@Nullable Map<String, Object> params) {
        logEvent(EagleTrackEvent.EventType.CLICK_ACTION, params);
    }

    public static void logInfoUpdate(@Nullable Map<String, Object> params) {
        logEvent(EagleTrackEvent.EventType.INFO_UPDATE, params);
    }

    public static void logPostVideoAction(@Nullable Map<String, Object> params) {
        logEvent(WeeeEvent.EVENT_POST_EDIT_VIDEO_CLICK, params);
    }

    public static void logStatusMetrics(@Nullable Map<String, Object> params) {
        logEvent(EagleTrackEvent.EventType.STATUS_METRICS, params);
    }
}
