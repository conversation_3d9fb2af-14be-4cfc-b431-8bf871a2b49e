package com.sayweee.weee.service.analytics;


import com.sayweee.analytics.config.IAnalyticsParams;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.global.manager.LanguageManager;
import com.sayweee.weee.global.manager.OrderManager;
import com.sayweee.weee.module.launch.service.StoreManager;
import com.sayweee.weee.service.helper.HeadersInterceptor;

import java.util.Map;

/**
 * Author:  winds
 * Date:    12/20/21.
 * Desc:
 */
public class AnalyticsParamsIml implements IAnalyticsParams {

    @Override
    public String getZipCode() {
        return OrderManager.get().getZipCode();
    }

    @Override
    public String getUserAgent() {
        return HeadersInterceptor.getInstance().getAgentInfo();
    }

    @Override
    public String getSessionId() {
        return AccountManager.get().getSessionToken();
    }

    @Override
    public String getLanguage() {
        return LanguageManager.get().getLanguage();
    }

    @Override
    public String getStore() {
        return StoreManager.get().getStoreKey();
    }

    @Override
    public String getUserId() {
        return AccountManager.get().getUserId();
    }

    @Override
    public String getOsLanguage() {
        return LanguageManager.get().getSystemLanguage();
    }

    @Override
    public Map<String, Object> getItemEventParams() {
        return null;
    }

    @Override
    public Map<String, Object> getPacketEventParams() {
        return null;
    }
}
