package com.sayweee.weee.service.helper;

import android.app.Activity;
import android.content.Context;
import android.graphics.Typeface;
import android.graphics.drawable.Drawable;
import android.text.style.StyleSpan;
import android.view.Gravity;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.core.content.ContextCompat;

import com.sayweee.weee.R;
import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.module.web.WebViewActivity;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.ViewTools;
import com.sayweee.weee.widget.CenterImageSpan;
import com.sayweee.weee.widget.WrapperPopWindow;
import com.sayweee.weee.widget.op.BubbleLayout;
import com.sayweee.wrapper.base.view.ViewHelper;
import com.sayweee.wrapper.helper.lifecycle.LifecycleProvider;
import com.sayweee.wrapper.listener.OnViewHelper;
import com.sayweee.wrapper.utils.Spanny;

/**
 * Author:  winds
 * Date:    2021/8/27.
 * Desc:
 */
public class AlcoholHelper {

    private AlcoholHelper(){}

    public static boolean showAlcoholIntroduce(Context context, View view, int extra) {
        if (!AccountManager.get().isAlcoholTipsDisplayed() && context != null && view != null && view.isAttachedToWindow()) {
            view.postDelayed(new Runnable() {
                @Override
                public void run() {
                    execShowAlcoholIntroduce(context, view, extra);
                    AccountManager.get().setAlcoholTipsDisplayed(true);
                }
            }, 150);
            return true;
        }
        return false;
    }

    private static void execShowAlcoholIntroduce(Context context, View view, int extra) {
        if (context != null && view != null && view.isAttachedToWindow()) {
            int[] location = new int[2];
            view.getLocationInWindow(location);
            int width = CommonTools.dp2px(246);
            int height = CommonTools.dp2px(145);
            int xOff = width - (view.getRight() - view.getLeft());
            int yOff = height + CommonTools.dp2px(extra);
            int x, y;
            int xDiff = 0;
            int yDiff = 0;
            if (location[0] > xOff) {
                x = location[0] - xOff;
            } else {
                x = CommonTools.dp2px(16);
                xDiff = location[0] - xOff - x;
            }
            if (location[1] > yOff) {
                y = location[1] - yOff;
            } else {
                y = 0;
            }
            int finalXDiff = xDiff;
            WrapperPopWindow popWindow = new WrapperPopWindow.PopupWindowBuilder(context)
                    .setView(ViewTools.getHelperView(context, R.layout.dialog_pantry, new OnViewHelper() {
                        @Override
                        public void help(ViewHelper helper) {
                            ((LinearLayout) helper.getView(R.id.layout_bubble_inner)).setGravity(Gravity.LEFT);
                            BubbleLayout layoutBubble = helper.getView(R.id.layout_bubble);
                            if (finalXDiff != 0) {
                                layoutBubble.setTriangleOffset(finalXDiff + layoutBubble.getTriangleOffset());
                            }
                            String title = context.getResources().getString(R.string.s_valid_id_needed);
                            TextView tvTitle = helper.getView(R.id.tv_title);
                            Drawable drawable = getAlcoholLightDrawable(view.getContext());
                            if (drawable != null) {
                                tvTitle.setText(new Spanny(" ", new CenterImageSpan(drawable)).append("  ").append(title, new StyleSpan(Typeface.BOLD)));
                            } else {
                                tvTitle.setText(title);
                            }
                            String text = context.getResources().getString(R.string.s_alcohol_introduce_content);
                            helper.setText(R.id.tv_content, text);
                        }
                    }))
                    .size(width, height)
                    .setFocusable(true)
                    .setOutsideTouchable(true)
                    .create();
            Activity activity;
            if(context instanceof Activity) {
                activity = (Activity) context;
            }else{
                activity = LifecycleProvider.get().getTopActivity();
            }
            if(activity == null) {
                return;
            }
            View decorView = activity.getWindow().getDecorView();
            popWindow.showAtLocation(decorView, Gravity.TOP | Gravity.LEFT, x, y);
            decorView.postDelayed(new Runnable() {
                @Override
                public void run() {
                    if (popWindow != null && popWindow.isShow()) {
                        popWindow.dismiss();
                    }
                }
            }, 3000);
        }
    }

    public static Drawable getAlcoholDrawable(Context context) {
        return getAlcoholLightDrawable(context);
    }

    public static Drawable getAlcoholLightDrawable(Context context) {
        Drawable drawable = ContextCompat.getDrawable(context, R.mipmap.ic_alcohol_flag);
        if (drawable != null) {
            drawable.setBounds(0, 0, CommonTools.dp2px(61.7f), CommonTools.dp2px(17));
        }
        return drawable;
    }

    public static void toAlcoholAgreement() {
        Activity activity = LifecycleProvider.get().getTopActivity();
        if (activity != null) {
            activity.startActivity(WebViewActivity.getIntent(activity, Constants.Url.AGREEMENT_OF_ALCOHOL));
        }
    }
}
