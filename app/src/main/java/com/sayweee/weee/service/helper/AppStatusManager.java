package com.sayweee.weee.service.helper;

import com.sayweee.monitor.WeeeMonitor;

public final class AppStatusManager {

    private boolean mLaunchFlowAbnormal;

    private AppStatusManager() {

    }

    private static final class Builder {
        private static final AppStatusManager sInstance = new AppStatusManager();
    }

    public static AppStatusManager get() {
        return Builder.sInstance;
    }

    public void setLaunchFlowAbnormal() {
        this.mLaunchFlowAbnormal = true;
        WeeeMonitor.getInstance().initAppAbnormal();
    }

    public boolean isLaunchFlowAbnormal() {
        return mLaunchFlowAbnormal;
    }



}
