package com.sayweee.weee.service.helper;

import android.app.Activity;
import android.app.Dialog;
import android.os.Build;
import android.view.View;

import com.sayweee.weee.R;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.module.dialog.CompatDialog;
import com.sayweee.weee.module.popup.PopupManager;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.wrapper.base.view.ViewHelper;
import com.sayweee.wrapper.base.view.WrapperDialog;
import com.sayweee.wrapper.helper.lifecycle.LifecycleProvider;
import com.sayweee.wrapper.listener.OnDialogClickListener;
import com.urbanairship.UAirship;

public class NotificationHelper {

    /**
     * 展示打开通知的提示
     */
    public static void showNotificationTips() {
        Activity activity = LifecycleProvider.get().getTopActivity();
        if (activity != null) {
            PopupManager.get().showOnQueue(new CompatDialog(activity, CompatDialog.STYLE_VERTICAL)
                    .setTitleUp(new OnDialogClickListener() {
                                    @Override
                                    public void onClick(WrapperDialog dialog, View view) {
                                        if (view.getId() == R.id.tv_confirm) {
                                            if (!CommonTools.isNotificationEnabled(activity)) {
                                                requestPermission();
                                            }
                                        }
                                        dialog.dismiss();
                                    }
                                }
                            , activity.getString(R.string.s_app_notifications)
                            , activity.getString(R.string.s_open_notification_tip)
                            , activity.getString(R.string.s_im_im)
                            , activity.getString(R.string.s_maybe_later)
                    )
                    .addHelperCallback(new WrapperDialog.HelperCallback() {
                        @Override
                        public void help(Dialog dialog, ViewHelper helper) {
                            helper.setOnClickListener(R.id.tv_cancel, new View.OnClickListener() {
                                @Override
                                public void onClick(View v) {
                                    dialog.dismiss();
                                }
                            });
                        }
                    }));
        }
    }

    public static void requestNotificationPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            requestPermission();
        } else {
            showNotificationTips();
        }
    }

    public static void checkUserNotificationsEnable() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            boolean isGuided = AccountManager.get().isGuided();
            if (isGuided) {
                boolean enabled = UAirship.shared().getPushManager().getUserNotificationsEnabled();
                UAirship.shared().getPushManager().setUserNotificationsEnabled(enabled);
            } else {
                UAirship.shared().getPushManager().setUserNotificationsEnabled(false);
            }
        } else {
            UAirship.shared().getPushManager().setUserNotificationsEnabled(true);
        }
    }

    private static void requestPermission() {
        Activity activity = LifecycleProvider.get().getTopActivity();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (UAirship.shared().getPushManager().getUserNotificationsEnabled()) {
                CommonTools.toNotificationSettings(activity);
            } else {
                UAirship.shared().getPushManager().setUserNotificationsEnabled(true);
            }
        } else {
            CommonTools.toNotificationSettings(activity);
        }
    }

}
