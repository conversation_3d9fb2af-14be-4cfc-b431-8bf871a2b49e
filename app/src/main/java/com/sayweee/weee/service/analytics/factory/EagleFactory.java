package com.sayweee.weee.service.analytics.factory;

import com.sayweee.weee.service.analytics.factory.iml.CardEagleFactory;
import com.sayweee.weee.service.analytics.factory.iml.CartInvalidProductEagleFactory;
import com.sayweee.weee.service.analytics.factory.iml.DealOfWeekEagleFactory;
import com.sayweee.weee.service.analytics.factory.iml.ItemMediumEagleFactory;
import com.sayweee.weee.service.analytics.factory.iml.ItemPostEagleFactory;
import com.sayweee.weee.service.analytics.factory.iml.ItemSmallEagleFactory;
import com.sayweee.weee.service.analytics.factory.iml.LightingDealEagleFactory;
import com.sayweee.weee.service.analytics.factory.iml.ListEagleFactory;
import com.sayweee.weee.service.analytics.factory.iml.PdpEagleFactory;

/**
 * Author:  winds
 * Date:    3/23/22.
 * Desc:
 */
public final class EagleFactory {

    private EagleFactory() {

    }

    public static IEagleFactory getFactory(EagleType type) {
        if (type == EagleType.TYPE_CARD) {
            return new CardEagleFactory();
        } else if (type == EagleType.TYPE_LIST) {
            return new ListEagleFactory();
        } else if (type == EagleType.TYPE_ITEM_SMALL) {
            return new ItemSmallEagleFactory();
        } else if (type == EagleType.TYPE_ITEM_MEDIUM) {
            return new ItemMediumEagleFactory();
        } else if (type == EagleType.TYPE_ITEM_POST) {
            return new ItemPostEagleFactory();
        } else if (type == EagleType.TYPE_DEAL_OF_WEEK) {
            return new DealOfWeekEagleFactory();
        } else if (type == EagleType.TYPE_ITEM_LIGHTING_DEAL) {
            return new LightingDealEagleFactory();
        } else if (type == EagleType.TYPE_ITEM_CART_INVALID_PRODUCT) {
            return new CartInvalidProductEagleFactory();
        } else if (type == EagleType.TYPE_PDP) {
            return new PdpEagleFactory();
        }
        return new CardEagleFactory();
    }
}
