package com.sayweee.weee.service.analytics.factory.iml;

import androidx.annotation.Nullable;

import com.sayweee.weee.module.cart.bean.CartItemBean;
import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.module.cart.service.OrderHelper;
import com.sayweee.weee.service.analytics.factory.BaseEagleFactory;

import java.io.Serializable;
import java.util.Map;


/**
 * Author:  winds
 * Date:    3/23/22.
 * Desc:
 */
public class CartInvalidProductEagleFactory extends BaseEagleFactory implements Serializable {

    @Nullable
    @Override
    protected Map<String, Object> createContent(ProductBean bean, int position) {
        return null;
    }

    @Override
    protected Map<String, Object> createCartContent(CartItemBean bean, int position) {
        Map<String, Object> params = getDefaultProductContent();
        params.put("prod_pos", position);
        params.put("prod_id", bean.product_id);
        params.put("recommendation_trace_id", bean.recommendation_trace_id);
        params.put("sold_status", "sold_out");
        params.put("restock_tip", bean.restock_tip);
        params.put("prod_name", bean.title);
        params.put("price", OrderHelper.formatConciseMoney(bean.price));
        if (bean.base_price > 0) {
            params.put("base_price", OrderHelper.formatConciseMoney(bean.base_price));
        }
        params.put("qty_in_cart", bean.quantity);
        return params;
    }

}
