package com.sayweee.weee.service.helper;

import androidx.annotation.NonNull;

import com.sayweee.weee.global.config.AppConfig;
import com.sayweee.weee.global.mmkv.MMKVManager;
import com.sayweee.weee.module.launch.bean.ConfigListBean;
import com.sayweee.weee.module.launch.bean.LaunchItemConfigBean;
import com.sayweee.weee.service.config.ConfigApi;
import com.sayweee.weee.service.rx.SimpleCompletableObserver;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.JsonUtils;
import com.sayweee.wrapper.bean.FailureBean;
import com.sayweee.wrapper.bean.ResponseBean;
import com.sayweee.wrapper.core.compat.ResponseObserver;
import com.sayweee.wrapper.http.RetrofitIml;
import com.sayweee.wrapper.http.converter.WrapperConverterFactory;
import com.sayweee.wrapper.http.interceptor.LogInterceptor;
import com.sayweee.wrapper.http.support.HttpsSupport;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import io.reactivex.Completable;
import io.reactivex.Observable;
import io.reactivex.ObservableOnSubscribe;
import io.reactivex.ObservableSource;
import io.reactivex.Observer;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.plugins.RxJavaPlugins;
import io.reactivex.schedulers.Schedulers;
import okhttp3.CertificatePinner;
import okhttp3.OkHttpClient;
import retrofit2.Retrofit;
import retrofit2.adapter.rxjava2.RxJava2CallAdapterFactory;

public final class NetworkCertPinner {

    private static final long SHORT_TIMEOUT = 5L; // 5s
    private static final String CONTENT_CONFIG_KEY = "launch_item_config";
    private static final String SIGN = "www.sayweeee.com";

    private static final String PIN_HASH_SHA256 = "sha256/";

    private static final String PIN_HOST_API_SAYWEEE_NET = "api.sayweee.net";
    private static final String PIN_HOST_API_SAYWEEE_NET_HASH_ROOT = "i7WTqTvh0OioIruIfFR4kMPnBqrS2rdiVPl/s2uC/CY=";

    private static final String PIN_HOST_API_TB1_SAYWEEE_NET = "api.tb1.sayweee.net";
    private static final String PIN_HOST_API_TB1_SAYWEEE_NET_HASH_ROOT = "++MBgDH5WGvL9Bcn5Be30cRcL0f5O+NyoXuWtQdX1aI=";

    private static final String CONFIG_KEY_V1 = "certificate_pinner_v1";

    private static final String CONFIG_KEY = CONFIG_KEY_V1;
    private static final String CONFIG_KEY_SAFE_NETWORK = "safe_network";

    private NetworkCertPinner() {

    }

    public static void obtainConfigFromLocal(@NonNull Runnable onComplete) {
        rxObtainConfigFromLocal()
                .flatMapCompletable(NetworkCertPinner::rxApplyPinner)
                .subscribe(new SimpleCompletableObserver(onComplete));
    }

    private static Observable<LaunchItemConfigBean> rxObtainConfigFromLocal() {
        return Observable.<LaunchItemConfigBean>create(emitter -> {
            String config = MMKVManager.obtain(MMKVManager.ID_CONFIG).decodeString(CONFIG_KEY, "");
            LaunchItemConfigBean bean = JsonUtils.parseObject(config, LaunchItemConfigBean.class);
            if (bean == null) {
                // enable certificate pinning
                bean = new LaunchItemConfigBean();
            }
            emitter.onNext(bean);
            emitter.onComplete();
        }).subscribeOn(Schedulers.io());
    }

    public static void obtainConfigFromServer(long delayMillis) {
        obtainConfigFromServer(delayMillis, () -> {

        });
    }

    public static void obtainConfigFromServer(long delayMillis, @NonNull Runnable onComplete) {
        Observable<Retrofit> observable = buildInsecureRetrofit();
        if (delayMillis > 0L) {
            observable = observable.delay(delayMillis, TimeUnit.MILLISECONDS);
        }
        observable.flatMap(NetworkCertPinner::rxObtainConfigFromServer)
                .flatMapCompletable(NetworkCertPinner::rxSaveServerConfig)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new SimpleCompletableObserver(onComplete));
    }

    private static Observable<Retrofit> buildInsecureRetrofit() {
        return Observable.create((ObservableOnSubscribe<Retrofit>) emitter -> {
            Retrofit.Builder rBuilder = new Retrofit.Builder();
            rBuilder.baseUrl(AppConfig.HOST);

            OkHttpClient.Builder cBuilder;
            cBuilder = new OkHttpClient.Builder();
            cBuilder.addInterceptor(HeadersInterceptor.getInstance());
            cBuilder.addInterceptor(new LogInterceptor(LogInterceptor.Level.BODY));
            cBuilder.readTimeout(SHORT_TIMEOUT, TimeUnit.SECONDS);
            cBuilder.connectTimeout(SHORT_TIMEOUT, TimeUnit.SECONDS);
            rBuilder.client(cBuilder.build());

            WrapperConverterFactory converterFactory = WrapperConverterFactory.create();
            converterFactory.setUseFastjson(true);
            rBuilder.addConverterFactory(converterFactory);
            rBuilder.addCallAdapterFactory(RxJava2CallAdapterFactory.create());

            emitter.onNext(rBuilder.build());
            emitter.onComplete();
        }).subscribeOn(Schedulers.io());
    }

    private static Observable<ResponseBean<String>> rxObtainConfigFromServer(@NonNull Retrofit retrofit) {
        return retrofit.create(ConfigApi.class)
                .getConfig(CONTENT_CONFIG_KEY)
                .flatMap(NetworkCertPinner::rxParseServerConfig);
    }

    private static Observable<ResponseBean<String>> rxParseServerConfig(ResponseBean<ConfigListBean> response) {
        ParseServerConfigObservable observable;
        observable = new ParseServerConfigObservable(Observable.just(response));
        return RxJavaPlugins.onAssembly(observable);
    }

    private static Completable rxSaveServerConfig(@NonNull ResponseBean<String> response) {
        return Completable.fromAction(() ->
                MMKVManager.obtain(MMKVManager.ID_CONFIG).encode(CONFIG_KEY, response.getData())
        );
    }

    private static Completable rxApplyPinner(@NonNull LaunchItemConfigBean bean) {
        return Completable.fromCallable(() -> NetworkCertPinner.applyPinner(bean));
    }

    private static boolean applyPinner(@NonNull LaunchItemConfigBean bean) {
        Retrofit oldRetrofit = RetrofitIml.get().getRetrofitClient();
        if (oldRetrofit == null) {
            return false;
        }
        OkHttpClient oldClient = null;
        if (oldRetrofit.callFactory() instanceof OkHttpClient) {
            oldClient = (OkHttpClient) oldRetrofit.callFactory();
        }
        if (oldClient == null) {
            return false;
        }

        Map<String, String> pinMap;
        if (!SIGN.equals(bean.associate_domain)) {
            pinMap = new HashMap<>(2);
            pinMap.put(PIN_HOST_API_SAYWEEE_NET, PIN_HOST_API_SAYWEEE_NET_HASH_ROOT);
            pinMap.put(PIN_HOST_API_TB1_SAYWEEE_NET, PIN_HOST_API_TB1_SAYWEEE_NET_HASH_ROOT);
        } else {
            pinMap = CollectionUtils.emptyMap();
        }
        CertificatePinner certificatePinner;
        if (!pinMap.isEmpty()) {
            CertificatePinner.Builder pinerBuilder = new CertificatePinner.Builder();
            for (Map.Entry<String, String> pin : pinMap.entrySet()) {
                pinerBuilder.add(pin.getKey(), PIN_HASH_SHA256 + pin.getValue());
            }
            certificatePinner = pinerBuilder.build();
        } else {
            certificatePinner = CertificatePinner.DEFAULT;
        }

        OkHttpClient.Builder newClientBuilder = oldClient.newBuilder()
                .certificatePinner(certificatePinner);
        Retrofit newRetrofit = oldRetrofit
                .newBuilder()
                .client(newClientBuilder.build())
                .build();
        RetrofitIml.get().clearCache();
        RetrofitIml.get().initHttp(newRetrofit);
        return true;
    }

    private static class ParseServerConfigObservable extends Observable<ResponseBean<String>> {

        private final ObservableSource<ResponseBean<ConfigListBean>> upstream;

        public ParseServerConfigObservable(ObservableSource<ResponseBean<ConfigListBean>> upstream) {
            this.upstream = upstream;
        }

        @Override
        protected void subscribeActual(Observer<? super ResponseBean<String>> observer) {
            ResponseObserver<ResponseBean<ConfigListBean>> obs = new ResponseObserver<ResponseBean<ConfigListBean>>() {

                @Override
                public void onResponse(ResponseBean<ConfigListBean> response) {
                    ResponseBean<String> newResponse = new ResponseBean<>();
                    newResponse.result = response.result;
                    newResponse.message = response.message;
                    newResponse.message_id = response.message_id;
                    newResponse.object = response.getData().launch_item_config;
                    observer.onNext(newResponse);
                }

                @Override
                public void onError(FailureBean failure) {
                    // ignore error
                    ResponseBean<String> mockResponse = new ResponseBean<>();
                    mockResponse.result = true;
                    mockResponse.object = "";
                    observer.onNext(mockResponse);
                }

                @Override
                public void onFinish() {
                    super.onFinish();
                    observer.onComplete();
                }
            };
            observer.onSubscribe(obs);
            upstream.subscribe(obs);
        }

    }

    public static boolean isSafeNetworkEnabled() {
        return MMKVManager.obtain(MMKVManager.ID_CONFIG).decodeBool(CONFIG_KEY_SAFE_NETWORK, true);
    }

    public static void setSafeNetworkEnabled(boolean enabled) {
        MMKVManager.obtain(MMKVManager.ID_CONFIG).encode(CONFIG_KEY_SAFE_NETWORK, enabled);
    }

    public static void applyUnsafeNetwork() {
        Retrofit oldRetrofit = RetrofitIml.get().getRetrofitClient();
        if (oldRetrofit == null) {
            return;
        }
        OkHttpClient oldClient = null;
        if (oldRetrofit.callFactory() instanceof OkHttpClient) {
            oldClient = (OkHttpClient) oldRetrofit.callFactory();
        }
        if (oldClient == null) {
            return;
        }

        HttpsSupport.SSLParams sslParams = HttpsSupport.getSslSocketFactory(HttpsSupport.UnSafeTrustManager);
        OkHttpClient.Builder newClientBuilder = oldClient.newBuilder()
                .sslSocketFactory(sslParams.sSLSocketFactory, sslParams.trustManager)
                .hostnameVerifier(HttpsSupport.UnSafeHostnameVerifier);
        Retrofit newRetrofit = oldRetrofit
                .newBuilder()
                .client(newClientBuilder.build())
                .build();
        RetrofitIml.get().clearCache();
        RetrofitIml.get().initHttp(newRetrofit);
    }

}
