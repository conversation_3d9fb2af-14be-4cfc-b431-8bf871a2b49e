package com.sayweee.weee.service.upload;

import androidx.annotation.NonNull;

import java.io.File;
import java.io.IOException;
import java.io.OutputStream;

import okhttp3.MediaType;
import okhttp3.RequestBody;
import okio.BufferedSink;
import okio.Okio;

public final class ProgressRequestBody extends RequestBody {

    private static final int SEGMENT_SIZE = 2048;

    private final long mId;

    @NonNull
    private final RequestBody mDelegate;

    @NonNull
    private final ProgressCallback mListener;

    public ProgressRequestBody(
            long id,
            @NonNull File file,
            @NonNull String contentType,
            @NonNull ProgressCallback listener
    ) {
        this.mId = id;
        this.mDelegate = RequestBody.create(MediaType.get(contentType), file);
        this.mListener = listener;
    }

    @Override
    public MediaType contentType() {
        return mDelegate.contentType();
    }

    @Override
    public long contentLength() throws IOException {
        try {
            return mDelegate.contentLength();
        } catch (IOException e) {
            mListener.onError(mId, e);
            throw e;
        }
    }

    @Override
    public void writeTo(@NonNull BufferedSink sink) throws IOException {
        try {
            OutputStream outputStream;
            outputStream = new ProgressOutputStream(sink.outputStream(), mListener, contentLength());
            BufferedSink progressSink = Okio.buffer(Okio.sink(outputStream));
            mDelegate.writeTo(progressSink);
            progressSink.flush();
        } catch (IOException e) {
            mListener.onError(mId, e);
            throw e;
        }
    }
}