package com.sayweee.weee.service.track;


import androidx.annotation.NonNull;
import androidx.collection.ArrayMap;

import java.util.Map;

/**
 * Author:  winds
 * Email:   <EMAIL>
 * Date:    2020/9/2.
 * Desc:
 */
public class TrackParams {

    ArrayMap<String, Object> params;

    public TrackParams() {
        this.params = new ArrayMap<>();
    }

    public TrackParams put(String key, Object value) {
        if (key != null) {
            params.put(key, value);
        }
        return this;
    }

    /**
     * 去除null 空字符串 -1 加入集合
     *
     * @param key
     * @param value
     * @return
     */
    public TrackParams putNonNull(String key, Object value) {
        if (value != null) {
            if (value instanceof String) {
                if ((((String) value).trim().length() == 0)) {
                    return this;
                }
            }
            if (value instanceof Integer) {
                if (((Integer) value) == -1) {
                    return this;
                }
            }
            put(key, value);
        }
        return this;
    }

    public TrackParams putAll(Map<String, Object> map) {
        if (map != null && map.size() > 0) {
            params.putAll(map);
        }
        return this;
    }

    public Object get(@NonNull String key) {
        return params.get(key);
    }

    public ArrayMap<String, Object> get() {
        return params;
    }

    public static class ChannelParams {
        public String type;
        public String channel;

        public ChannelParams(String type, String channel) {
            this.type = type;
            this.channel = channel;
        }

        public ChannelParams(String channel) {
            this.channel = channel;
        }

        public Map<String, Object> get() {
            return new TrackParams().putNonNull("type", type).putNonNull("channel", channel).get();
        }
    }


}
