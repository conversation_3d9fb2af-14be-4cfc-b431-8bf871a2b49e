package com.sayweee.weee.service.helper;

import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import androidx.annotation.NonNull;

import com.sayweee.analytics.WeeeAnalytics;
import com.sayweee.analytics.WeeeAnalyticsIml;
import com.sayweee.analytics.platform.WeeeExecutor;
import com.sayweee.logger.Logger;
import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.global.config.DevConfig;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.global.manager.LanguageManager;
import com.sayweee.weee.global.manager.OrderManager;
import com.sayweee.weee.module.search.v2.SearchResultsFragmentV2;
import com.sayweee.weee.module.search.v2.SearchV2Manager;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.JsonUtils;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.IOException;
import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okio.Buffer;

/**
 * Author:  winds
 * Email:   <EMAIL>
 * Date:    2020/6/1.
 * Desc:
 */
public class SearchTrackInterceptor implements Interceptor {

    private static final String TAG = "NewSearchTrackInterceptor";

    public static final boolean TRACK_EVENT_DEBUG_CONSOLE = DevConfig.isDebug() && true;
    public static final boolean TRACK_EVENT_DEBUG_CALL_INTERCEPT = (DevConfig.isTb1()) && false;
    public static final boolean TRACK_EVENT_DEBUG_REQUEST_INTERCEPT = DevConfig.isTb1() && false;

    public SearchTrackInterceptor() {
    }

    @NonNull
    @Override
    public Response intercept(Chain chain) throws IOException {
        Request original = chain.request();
        if (SearchTrackInterceptor.TRACK_EVENT_DEBUG_REQUEST_INTERCEPT) {
            try {
                String body = bodyToString(original);
                if (body != null && !body.isEmpty()) {
                    if ("POST".equals(original.method()) && original.url().encodedPath().equals("/bi/events/track") && body.contains("event_items")) {
                        JSONObject bodyJson = new JSONObject(body);
                        if (bodyJson != null) {
                            JSONArray eventItems = bodyJson.optJSONArray("event_items");
                            if (eventItems != null) {
                                int eventItemsLength = eventItems.length();
                                for (int i = 0; i < eventItemsLength; i++) {
                                    JSONObject eventItem = eventItems.getJSONObject(i);
                                    if (eventItem != null) {
                                        logEventMessageFromRequest(eventItem);
                                    }
                                }
                            }
                        }
                    }
                }
            } catch (Throwable e) {
                Log.e(TAG, "intercept error: " + e.getLocalizedMessage(), e);
            }
        }
        return chain.proceed(original);
    }

    public static void logEventMessage(String eventName, Map<String, Object> params) {
        String eventLogDescription = getEventLogDescription(eventName, params);
        if (eventLogDescription != null) {
            final String userId = AccountManager.get().getUserId();
            final boolean isLoggedIn = userId != null && !userId.isEmpty();
            final String zipcode = OrderManager.get().getZipCode();
            final String language = LanguageManager.get().getLanguage();
            final String primaryStore = SearchV2Manager.get().getPrimaryStore();

            final String logHeader = "\n*u:* " + (isLoggedIn ? userId : "(not logged)") + "\n*z:* " + zipcode + "\n*ps*: " + primaryStore + "\n*lang:* " + language;

            final String eventLogMessage = logHeader + "\n" + eventLogDescription;

            if (SearchTrackInterceptor.TRACK_EVENT_DEBUG_CALL_INTERCEPT) {
                //SlackManager.get().sendGrowthMobileDbgMsg(eventLogMessage, params);
            }

            if (SearchTrackInterceptor.TRACK_EVENT_DEBUG_CONSOLE) {
                JSONObject obj = !EmptyUtils.isEmpty(params)
                        ? new JSONObject(new HashMap<>(params))
                        : JsonUtils.EMPTY_JSON_OBJECT;
                Logger.d(TAG, "[new-search-track-interceptor] from:CALL " + eventLogMessage, obj.toString());
            }

            if (SearchTrackInterceptor.TRACK_EVENT_DEBUG_REQUEST_INTERCEPT) {
                Looper looper = Looper.myLooper();
                if (looper == null) looper = Looper.getMainLooper();
                new Handler(looper).postDelayed(() -> {
                    SearchTrackInterceptor.flushTrackMessages();
                }, 500);
            }
        }
    }

    private static void logEventMessageFromRequest(JSONObject eventItem) {
        String eventName = eventItem.optString("type");
        JSONObject params = eventItem.optJSONObject("params");

        String eventLogDescription = getEventLogDescription(eventName, params);
        if (eventLogDescription != null) {
            final String userId = eventItem.optString("user_id");
            final boolean isLoggedIn = userId != null && !userId.isEmpty();
            final String zipcode = eventItem.optString("zipcode");
            final String language = eventItem.optString("language");
            final String store = eventItem.optString("store", "cn");

            String primaryStore;
            if ("en".equals(language)) {
                primaryStore = "store_" + store;
            } else {
                primaryStore = "lang_" + language;
            }

            final String logHeader = "*u:* "+(isLoggedIn ? userId : "(not logged)")+"\n*z:* "+zipcode+"\n*st:* "+store+"\n*ps*: "+primaryStore+"\n*lang:* "+language;

            final String eventLogMessage = logHeader + "\n" + eventLogDescription;

            if (SearchTrackInterceptor.TRACK_EVENT_DEBUG_REQUEST_INTERCEPT) {
                //SlackManager.get().sendGrowthMobileDbgMsg(eventLogMessage, eventItem);
            }

            if (SearchTrackInterceptor.TRACK_EVENT_DEBUG_CONSOLE) {
                Logger.d(TAG, "[new-search-track-interceptor] from:REQUEST " + eventLogMessage);
            }
        }
    }

    private static String getEventLogDescription(String eventName, Map<String, Object> params) {
        if (params == null) return null;
        JSONObject obj = null;
        try {
            obj = !EmptyUtils.isEmpty(params)
                    ? new JSONObject(new HashMap<>(params))
                    : JsonUtils.EMPTY_JSON_OBJECT;
        } catch (Throwable e) {
            Logger.enable(DevConfig.isDebug()).e("getEventLogDescription ERROR eventName:" + eventName, e);
        }
        return getEventLogDescription(eventName, obj);
    }

    private static String getEventLogDescription(String eventName, JSONObject params) {
        final JSONObject ctx = params != null ? params.optJSONObject("ctx") : null;
        if (ctx != null) {
            final boolean isSearchV2Track = SearchResultsFragmentV2.TRACK_CONTEXT_PARAM_SEARCH_V2.equals(ctx.optString(Constants.SearchV2.PLATFORM));
            if (isSearchV2Track) {
                String eventType = "unknown";
                if (EagleTrackEvent.EventType.ADS_BANNER_IMP.equals(eventName) || EagleTrackEvent.EventType.PROD_IMP.equals(eventName) || EagleTrackEvent.EventType.ADS_PROD_IMP.equals(eventName)) {
                    eventType = "impression";
                }
                else if (EagleTrackEvent.EventType.CART_ACTION.equals(eventName) || EagleTrackEvent.EventType.ADS_CART_ACTION.equals(eventName)) {
                    eventType = "conversion";
                }
                else if (EagleTrackEvent.EventType.CLICK_ACTION.equals(eventName)) {
                    eventType = "click";
                }

                String logMessage = "*Type:* "+eventType;

                final JSONObject co = params.optJSONObject("co");
                if (co != null) {
                    logMessage = logMessage + "\n*Event:* " + eventName;

                    String searchQuery = !ctx.isNull("page_target") ? ctx.optString("page_target", null) : null;
                    if (searchQuery != null && !searchQuery.isEmpty()) {
                        logMessage = logMessage + "\n*Query:* "+searchQuery;
                    }

                    boolean skipOthers = false;

                    final String bannerType = !co.isNull("banner_type") ? co.optString("banner_type", null) : null;
                    if (bannerType != null) {
                        final String bannerId = co.optString("banner_id");
                        final String bannerPos = co.optString("banner_pos", "");
                        logMessage = logMessage+"\n*BannerType:* "+bannerType+"\n*BannerId:* "+bannerId+"\nBannerPos:* " + bannerPos;
                        skipOthers = true;
                    }

                    if (!skipOthers) {
                        final String position = !co.isNull("prod_pos") ? co.optString("prod_pos", null) : null;
                        if (position != null) {
                            final String sku = co.optString("prod_id");
                            final String sectionType = ctx.optString("section_type");
                            final boolean isMain = SearchResultsFragmentV2.TRACK_EVENT_SECTION_TYPE_MAIN.equals(sectionType);
                            logMessage = logMessage + "\n*Section:* " + (isMain ? "MAIN" : "SECONDARY") + "\n*Position:* " + position + "\n*Sku:* " + sku;
                            skipOthers = true;
                        }
                    }

                    if (!skipOthers) {
                        final String targetType = !co.isNull("target_type") ? co.optString("target_type", null) : null;
                        if (!EmptyUtils.isEmpty(targetType)) {
                            logMessage = logMessage + "\n*TargetType:* " + targetType;

                            final String targetName = !co.isNull("target_nm") ? co.optString("target_nm", null) : null;
                            if (!EmptyUtils.isEmpty(targetName)) {
                                logMessage = logMessage + "\n*TargetName:* " + targetName;
                            }

                            final String targetPosition = !co.isNull("target_pos") ? co.optString("target_pos", null) : null;
                            if (!EmptyUtils.isEmpty(targetPosition)) {
                                logMessage = logMessage + "\n*TargetPosition:* " + targetPosition;
                            }
                        }
                    }
                }

                String modNm = !params.isNull("mod_nm") ? params.optString("mod_nm", null) : null;
                if (!EmptyUtils.isEmpty(modNm)) {
                    logMessage = logMessage + "\n*mod_nm:* " + modNm;
                }

                Integer modPos = !params.isNull("mod_pos") ? params.optInt("mod_pos", -1) : null;
                if (modNm != null) {
                    logMessage = logMessage + "\n*mod_pos:* " + modPos;
                }

                String secNm = !params.isNull("sec_nm") ? params.optString("sec_nm", null) : null;
                if (!EmptyUtils.isEmpty(secNm)) {
                    logMessage = logMessage + "\n*sec_nm:* " + secNm;
                }

                Integer secPos = !params.isNull("sec_pos") ? params.optInt("sec_pos", -1) : null;
                if (secNm != null) {
                    logMessage = logMessage + "\n*sec_pos:* " + secPos;
                }

                String sectionId = ctx.optString(SearchResultsFragmentV2.TRACK_EVENT_PARAM_SECTION_ID);
                String sectionType = ctx.optString(SearchResultsFragmentV2.TRACK_EVENT_PARAM_SECTION_TYPE);
                String sectionDisplayType = ctx.optString(SearchResultsFragmentV2.TRACK_EVENT_PARAM_SECTION_DISPLAY_TYPE);
                String pageTarget = ctx.optString(EagleTrackEvent.Ctx.PAGE_TARGET);
                String sectionTag = ctx.optString( Constants.SearchV2.SEARCH_SECTION_TAG);
                String subSectionId = ctx.optString(SearchResultsFragmentV2.TRACK_EVENT_PARAM_SUB_SECTION_ID);
                String subSectionTag = ctx.optString(SearchResultsFragmentV2.TRACK_EVENT_PARAM_SUB_SECTION_TAG);
                String sorts = ctx.optString("sort");
                String filters = ctx.optString("filters");

                logMessage = logMessage + "\n*page_target*: "+pageTarget;
                logMessage = logMessage + "\n*section_id*: "+sectionId;
                logMessage = logMessage + "\n*section_type*: "+sectionType;
                logMessage = logMessage + "\n*section_display_type*: "+sectionDisplayType;
                logMessage = logMessage + "\n*section_analytics_tag*: "+sectionTag;
                logMessage = logMessage + "\n*sub_section_id*: "+subSectionId;
                logMessage = logMessage + "\n*sub_section_tag*: "+subSectionTag;
                logMessage = logMessage + "\n*sorts*: "+sorts;
                logMessage = logMessage + "\n*filters*: "+filters;

                return logMessage;
            }
        }

        return null;
    }

    private static void flushTrackMessages() {
        if (!TRACK_EVENT_DEBUG_REQUEST_INTERCEPT) return;

        try {
            WeeeAnalytics analytics = WeeeAnalytics.get();
            if (analytics instanceof WeeeAnalyticsIml) {
                WeeeAnalyticsIml impl = (WeeeAnalyticsIml) analytics;
                Field field = impl.getClass().getDeclaredField("platform");
                field.setAccessible(true);
                WeeeExecutor value = (WeeeExecutor) field.get(impl);
                value.uploadAll();
                Logger.d(TAG, "[new-search-track-interceptor] uploadAll");
            }
        } catch (Throwable e) {
            Log.e(TAG, "flush ERROR:" + e.getLocalizedMessage());
        }
    }

    private static String bodyToString(final Request request){
        try {
            final Request copy = request.newBuilder().build();
            RequestBody body = copy.body();
            if (body != null) {
                final Buffer buffer = new Buffer();
                body.writeTo(buffer);
                return buffer.readUtf8();
            }
        } catch (final IOException ignore) {
        }

        return null;
    }
}
