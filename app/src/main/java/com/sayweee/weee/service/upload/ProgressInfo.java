package com.sayweee.weee.service.upload;

public class ProgressInfo {

    private final long id;
    private final long currentBytes;
    private final long contentLength;
    private final float percent;
    private final float speed;

    public ProgressInfo(long id) {
        this(id, -1L, -1L, -1f, -1f);
    }

    public ProgressInfo(long id, long currentBytes, long contentLength, float percent, float speed) {
        this.id = id;
        this.currentBytes = currentBytes;
        this.contentLength = contentLength;
        this.percent = percent;
        this.speed = speed;
    }

    public long getId() {
        return id;
    }

    public long getCurrentBytes() {
        return currentBytes;
    }

    public long getContentLength() {
        return contentLength;
    }

    public float getPercent() {
        return percent;
    }

    public float getSpeed() {
        return speed;
    }
}
