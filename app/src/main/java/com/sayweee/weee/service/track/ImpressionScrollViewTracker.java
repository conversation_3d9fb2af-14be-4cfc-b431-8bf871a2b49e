package com.sayweee.weee.service.track;

import android.os.Build;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.ScrollView;

import androidx.core.widget.NestedScrollView;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;

import com.sayweee.weee.module.home.bean.ImpressionBean;

import java.util.ArrayList;
import java.util.List;

/**
 * Author:  winds
 * Email:   <EMAIL>
 * Date:    2021/3/26.
 * Desc:
 */
public abstract class ImpressionScrollViewTracker<T extends ImpressionBean> {

    public static final int IMPRESSION_LIMIT = 1000;

    private boolean disable;
    private boolean skipFirstResume;
    private int resumeCount;

    public ImpressionScrollViewTracker() {
        impressionEventHistory = new ArrayList<>();
    }

    public ImpressionScrollViewTracker setImpressionEnable(boolean enable) {
        this.disable = !enable;
        return this;
    }

    private boolean isImpressionEnable() {
        return !disable;
    }

    //1. 设置数据变化刷新布局时触发
    //2. 滚动停止时触发
    //3. 事件频率随pv
    public void trackImpression(RecyclerView targetView) {
        if (isImpressionEnable() && targetView != null && targetView.isAttachedToWindow()) {
            RecyclerView.Adapter adapter = targetView.getAdapter();
            if (adapter instanceof ImpressionAdapter && adapter.getItemCount() > 0) {
                RecyclerView.LayoutManager layoutManager = targetView.getLayoutManager();
                int firstVisibleItemPosition = RecyclerView.NO_POSITION;
                int lastVisibleItemPosition = RecyclerView.NO_POSITION;
                if (layoutManager instanceof LinearLayoutManager) {
                    LinearLayoutManager manager = (LinearLayoutManager) layoutManager;
                    firstVisibleItemPosition = manager.findFirstVisibleItemPosition();
                    lastVisibleItemPosition = manager.findLastVisibleItemPosition();
                } else if (layoutManager instanceof StaggeredGridLayoutManager) {
                    StaggeredGridLayoutManager manager = (StaggeredGridLayoutManager) layoutManager;
                    int[] temp = new int[2];
                    manager.findFirstVisibleItemPositions(temp);
                    firstVisibleItemPosition = Math.min(temp[0], temp[1]);
                    manager.findLastVisibleItemPositions(temp);
                    lastVisibleItemPosition = Math.max(temp[0], temp[1]);
                }
                if (firstVisibleItemPosition == RecyclerView.NO_POSITION || lastVisibleItemPosition == RecyclerView.NO_POSITION) {
                    return;
                }
                List<T> impressionData = ((ImpressionAdapter) adapter).getImpressionData(firstVisibleItemPosition, lastVisibleItemPosition);
                if (impressionData == null || impressionData.isEmpty() || impressionEventHistory.containsAll(impressionData)) {
                    //过滤 此次的埋点已经全部上报
                    return;
                }
                findTargetImpression(targetView, impressionData);
            }
        }
    }

    private void findTargetImpression(RecyclerView targetView, List<T> lastData) {
        if (targetView != null && targetView.isAttachedToWindow()) {
            targetView.postDelayed(new Runnable() {
                @Override
                public void run() {
                    if (targetView.isAttachedToWindow()) {
                        RecyclerView.Adapter adapter = targetView.getAdapter();
                        if (adapter instanceof ImpressionAdapter && adapter.getItemCount() > 0) {
                            int firstVisibleItemPosition = RecyclerView.NO_POSITION;
                            int lastVisibleItemPosition = RecyclerView.NO_POSITION;
                            RecyclerView.LayoutManager layoutManager = targetView.getLayoutManager();
                            if (layoutManager instanceof LinearLayoutManager) {
                                LinearLayoutManager manager = (LinearLayoutManager) layoutManager;
                                firstVisibleItemPosition = manager.findFirstVisibleItemPosition();
                                lastVisibleItemPosition = manager.findLastVisibleItemPosition();
                            } else if (layoutManager instanceof StaggeredGridLayoutManager) {
                                int[] temp = new int[2];
                                StaggeredGridLayoutManager manager = (StaggeredGridLayoutManager) layoutManager;
                                manager.findFirstVisibleItemPositions(temp);
                                firstVisibleItemPosition = Math.min(temp[0], temp[1]);
                                manager.findLastVisibleItemPositions(temp);
                                lastVisibleItemPosition = Math.max(temp[0], temp[1]);
                            }

                            if (firstVisibleItemPosition == RecyclerView.NO_POSITION || lastVisibleItemPosition == RecyclerView.NO_POSITION) {
                                return;
                            }
                            List<T> impressionData = ((ImpressionAdapter) adapter).getImpressionData(firstVisibleItemPosition, lastVisibleItemPosition);
                            impressionData.retainAll(lastData);
                            if (impressionData.size() > 0) {
                                trackImpressionEvent(impressionData);
                            }
                        }
                    }
                }
            }, IMPRESSION_LIMIT);
        }
    }

    public void trackImpression(ScrollView view, ImpressionAdapter adapter) {
        int top = view.getScrollY();
        int bottom = top + view.getHeight();
        View container = view.getChildAt(0);
        if (container instanceof LinearLayout) {
            LinearLayout parent = (LinearLayout) container;
            int count = parent.getChildCount();
            if (count > 0) {
                int total = 0;
                int start = -1;
                int end = -1;
                for (int i = 0; i < count; i++) {
                    View child = parent.getChildAt(i);
                    if (child != null && child.getVisibility() != View.GONE) {
                        if (total >= top) {
                            if (start == -1) {
                                start = i;
                            }
                        }
                        total += child.getHeight();
                        if (total > bottom) {
                            end = i;
                            break;
                        }
                    }
                }
                if (start != -1) {
                    List<T> impressionData = adapter.getImpressionData(start, Math.max(end, start));
                    if (impressionData != null && impressionData.size() > 0) {
                        trackImpressionEvent(impressionData);
                    }
                }
            }
        }
    }


    public void trackImpression(NestedScrollView view) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            view.setOnScrollChangeListener(new View.OnScrollChangeListener() {
                @Override
                public void onScrollChange(View v, int scrollX, int scrollY, int oldScrollX, int oldScrollY) {

                }
            });
        }

    }

    /**
     * 某些列表数据在其他页面被带入，在onResume前数据已被上报一次，此方法可以避免此情况
     *
     * @param skip
     */
    public void setSkipFirstResume(boolean skip) {
        this.skipFirstResume = skip;
    }

    public void onPageResume(RecyclerView targetView) {
        ++resumeCount;
        if (impressionEventHistory == null) {
            impressionEventHistory = new ArrayList<>();
        }
        if (resumeCount != 1 || !skipFirstResume) {
            impressionEventHistory.clear();
        }
        trackImpression(targetView);
    }

    public boolean isTracked(String eventKey) {
        return impressionEventHistory != null && eventKey != null && impressionEventHistory.contains(eventKey);
    }

    public void setTracked(String eventKey) {
        if (impressionEventHistory == null) {
            impressionEventHistory = new ArrayList<>();
        }
        if (eventKey != null) {
            impressionEventHistory.add(eventKey);
        }
    }

    protected List<String> impressionEventHistory;

    protected void trackImpressionEvent(List<T> impressionData) {
        for (T event : impressionData) {
            if (event != null) {
                trackImpression(event);
            }
        }
    }

    public abstract void trackImpression(T event);

}
