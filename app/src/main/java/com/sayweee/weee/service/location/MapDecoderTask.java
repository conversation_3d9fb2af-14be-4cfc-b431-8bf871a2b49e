package com.sayweee.weee.service.location;

import android.graphics.Bitmap;
import android.widget.ImageView;

import com.google.android.gms.maps.model.LatLng;

import java.lang.ref.SoftReference;

/**
 * Author:  winds
 * Date:    12/23/21.
 * Desc:
 */
public class MapDecoderTask {
    public LatLng latLng;
    public Bitmap displayBitmap;
    public SoftReference<ImageView> imageViewRef;

    public MapDecoderTask(LatLng latLng, Bitmap displayBitmap, ImageView imageView) {
        this.latLng = latLng;
        this.displayBitmap = displayBitmap;
        this.imageViewRef = new SoftReference<>(imageView);
    }
}
