package com.sayweee.weee.service.helper;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.sayweee.cache.CacheManager;
import com.sayweee.cache.CacheRequest;
import com.sayweee.cache.CacheResult;
import com.sayweee.weee.utils.JsonUtils;

import java.lang.reflect.Type;

import io.reactivex.Observable;
import io.reactivex.ObservableSource;

public class CacheHelper {

    public static void initCache(Application application) {
        CacheManager.getInstance().prepare(application, "_weee_cache");
    }

    public static <S> Observable<S> readData(String key, Type type) {
        return Observable.just(new CacheRequest(key, type)).flatMap(CacheHelper::rxReadData);
    }

    public static Observable<Boolean> saveData(String key, Object obj) {
        if (key == null) {
            return Observable.just(false);
        }
        return Observable.just(true).flatMap(sig -> rxSaveData(key, obj));
    }

    private static <S> ObservableSource<S> rxReadData(@NonNull CacheRequest request) {
        CacheResult result = CacheManager.getInstance().readFromCache(request.key, request.cacheValidTime);
        S s;
        if (result.data != null && !result.data.isEmpty()) {
            s = JsonUtils.parseObject(result.data, request.type);
        } else {
            s = null;
        }
        return s != null ? Observable.just(s) : Observable.error(new NullPointerException());
    }

    private static ObservableSource<Boolean> rxSaveData(@NonNull String key, @Nullable Object obj) {
        String data = obj != null ? JsonUtils.toJSONString(obj) : null;
        boolean success = CacheManager.getInstance().writeToCacheDirect(key, data);
        return Observable.just(success);
    }


}
