package com.sayweee.weee.service.shortcut;

import android.app.Application;
import android.content.Intent;
import android.net.Uri;

import androidx.annotation.DrawableRes;
import androidx.core.content.pm.ShortcutInfoCompat;
import androidx.core.content.pm.ShortcutManagerCompat;
import androidx.core.graphics.drawable.IconCompat;

import com.sayweee.service.ConfigService;
import com.sayweee.weee.R;
import com.sayweee.weee.global.config.AppConfig;
import com.sayweee.weee.global.manager.LanguageManager;
import com.sayweee.weee.service.config.ConfigManager;
import com.sayweee.weee.service.config.bean.ShortcutConfigBean;
import com.sayweee.wrapper.helper.lifecycle.LifecycleProvider;

import java.util.ArrayList;
import java.util.List;

/**
 * Author:  winds
 * Date:    2022/7/22.
 * Desc:
 */
public class ShortcutHelper {

    public interface Shortcut {
        String SEARCH = "search";
        String COLLECT = "collect";
        String CART = "cart";
        String ORDER = "order";
    }

    public static void applyShortcut() {
        ShortcutConfigBean config = ConfigService.get().getDynamicConfig(ConfigManager.DynamicConfig.SHORTCUT);
        applyShortcut(config);
    }

    public static void applyShortcut(ShortcutConfigBean config) {
        Application context = LifecycleProvider.get().getApplication();
        if (context != null) {
            if (config != null && config.data != null && config.data.size() > 0) {
                String tag = LanguageManager.get().getSystemLanguageOnNull();
                String lang = tag;
                if (!config.data.containsKey(tag)) {
                    lang = (tag.equals(LanguageManager.Language.TRADITIONAL_CHINESE) || tag.equals(LanguageManager.Language.CHINESE))
                            ? LanguageManager.Language.CHINESE : LanguageManager.Language.ENGLISH;
                }
                List<ShortcutConfigBean.ShortcutItemBean> beans = config.data.get(lang);
                if (beans != null && beans.size() > 0 && context != null) {
                    List<ShortcutInfoCompat> list = new ArrayList<>();
                    for (ShortcutConfigBean.ShortcutItemBean bean : beans) {
                        if (bean != null && bean.path != null) {
                            ShortcutInfoCompat.Builder builder = new ShortcutInfoCompat.Builder(context, bean.key);
                            String path = bean.path.startsWith("http") ? bean.path : AppConfig.HOST_WEB + (bean.path.startsWith("/") ? bean.path : "/" + bean.path);
                            Intent intent = context.getPackageManager().getLaunchIntentForPackage(context.getPackageName());
                            intent.setData(Uri.parse(path));
                            builder
                                    .setShortLabel(bean.name)
                                    .setIcon(IconCompat.createWithResource(context, getShortcutIcon(bean.key)))
                                    .setIntent(intent);
                            list.add(builder.build());
                        }
                    }
                    ShortcutManagerCompat.addDynamicShortcuts(context, list);
                    return;
                }
            }
            ShortcutManagerCompat.removeAllDynamicShortcuts(context);
        }
    }


    @DrawableRes
    public static int getShortcutIcon(String key) {
        if (key != null) {
            switch (key) {
                case Shortcut.SEARCH:
                    return R.mipmap.shortcut_search;
                case Shortcut.COLLECT:
                    return R.mipmap.shortcut_collect;
                case Shortcut.CART:
                    return R.mipmap.shortcut_cart;
                case Shortcut.ORDER:
                    return R.mipmap.shortcut_order;
            }
        }
        return R.mipmap.shortcut_default;
    }
}
