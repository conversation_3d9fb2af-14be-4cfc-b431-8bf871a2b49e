package com.sayweee.weee.service.pay;

import android.app.Activity;

import androidx.annotation.NonNull;

import com.sayweee.logger.Logger;
import com.sayweee.weee.global.config.Constants;
import com.sayweee.weee.global.config.DevConfig;
import com.sayweee.weee.module.cart.service.OrderApi;
import com.sayweee.weee.module.checkout.bean.CashAppPayBean;
import com.sayweee.weee.module.checkout.bean.CitconPayBean;
import com.sayweee.weee.module.checkout.bean.CitconTokenBean;
import com.sayweee.weee.service.rx.DisposableTransformer;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.wrapper.bean.FailureBean;
import com.sayweee.wrapper.bean.ResponseBean;
import com.sayweee.wrapper.core.compat.ResponseObserver;
import com.sayweee.wrapper.core.compat.ResponseTransformer;
import com.sayweee.wrapper.helper.lifecycle.LifecycleProvider;
import com.sayweee.wrapper.http.support.RequestParams;

import java.io.Serializable;
import java.util.List;
import java.util.Locale;

import io.reactivex.Observable;
import sdk.CPayLaunchType;
import sdk.CPaySDK;
import sdk.interfaces.InquireResponse;
import sdk.interfaces.OrderResponse;
import sdk.models.CPayInquireResult;
import sdk.models.CPayOrder;
import sdk.models.CPayOrderResult;

public final class CitconPaymentHelper {

    private CitconPaymentHelper() {

    }

    //*************************************************************************************************/

    /**
     * alipay by citcon
     **/
    static void onCitconPayResume() {
        try {
            //因为用到时才初始化，存在获取token时还未初始化的情况，故增加保护
            String mToken = CPaySDK.getInstance().mToken;
            if (mToken != null) {
                Logger.json("CitconPaymentHelper", "CPaySDK::onResume()");
                CPaySDK.getInstance().onResume();
            }
        } catch (Exception ignored) {
        }
    }

    private static void prepareCitconData(@NonNull CitconTokenBean data) {
        CPaySDK.initInstance(LifecycleProvider.get().getTopActivity(), PaymentHelper.decrypt(data.token));
        CPaySDK.setMode(data.mode);
    }

    static void prepareAliPayByCitcon(boolean isCombinePay, @NonNull List<Integer> orderIds, double amount, @NonNull PaymentHelper.OnPaymentCallback callback) {
        OrderApi loader = PaymentHelper.getLoader();
        if (loader == null || EmptyUtils.isEmpty(orderIds)) {
            return;
        }

        Observable<ResponseBean<CitconPayBean>> payDataObservable = getCitconAlipayPayDataObservable(loader, isCombinePay, orderIds, amount);

        Observable<ResponseBean<CitconTokenBean>> citconTokenObservable = loader.getCitconToken()
                .compose(ResponseTransformer.scheduler());

        Observable.merge(citconTokenObservable, payDataObservable)
                .subscribe(getCitconPayDataObserver(Constants.PayType.ALIPAY_K, isCombinePay, orderIds, callback));
    }

    private static Observable<ResponseBean<CitconPayBean>> getCitconAlipayPayDataObservable(
            OrderApi loader,
            boolean isCombinePay,
            @NonNull List<Integer> orderIds,
            double amount
    ) {
        Observable<ResponseBean<CitconPayBean>> payDataObservable;
        RequestParams params = new RequestParams().put("amount", amount).put("currency", "USD").put("version", "V1").put("platform", "app");
        if (isCombinePay) {
            payDataObservable = loader.getAlipayDataByCitcon(params.putNonNull("order_ids", (Serializable) orderIds).get());
        } else {
            payDataObservable = loader.getAlipayDataByCitconV1(params.putNonNull("purchase_id", orderIds.get(0)).get());
        }
        payDataObservable = payDataObservable.compose(DisposableTransformer.scheduler());
        return payDataObservable;
    }

    //*************************************************************************************************/

    /**
     * wechat pay by citcon
     **/

    static void prepareWechatPayByCitcon(boolean isCombinePay, @NonNull List<Integer> orderIds, double amount, @NonNull PaymentHelper.OnPaymentCallback callback) {
        OrderApi loader = PaymentHelper.getLoader();
        if (loader == null || EmptyUtils.isEmpty(orderIds)) {
            return;
        }

        Observable<ResponseBean<CitconPayBean>> payDataObservable = getCitconWechatPayDataObservable(loader, isCombinePay, orderIds, amount);

        Observable<ResponseBean<CitconTokenBean>> citconTokenObservable = loader.getCitconToken()
                .compose(ResponseTransformer.scheduler());

        Observable.merge(citconTokenObservable, payDataObservable)
                .subscribe(getCitconPayDataObserver(Constants.PayType.WECHAT, isCombinePay, orderIds, callback));
    }

    private static Observable<ResponseBean<CitconPayBean>> getCitconWechatPayDataObservable(
            OrderApi loader,
            boolean isCombinePay,
            @NonNull List<Integer> orderIds,
            double amount
    ) {
        RequestParams params = new RequestParams()
                .put("amount", amount)
                .put("currency", "USD")
                .put("version", "V1")
                .put("platform", "app");
        Observable<ResponseBean<CitconPayBean>> payDataObservable;
        if (isCombinePay) {
            payDataObservable = loader.getWechatPayDataByCitcon(params.putNonNull("order_ids", (Serializable) orderIds).get());
        } else {
            payDataObservable = loader.getWechatPayDataByCitconV1(params.putNonNull("purchase_id", orderIds.get(0)).get());
        }
        payDataObservable = payDataObservable.compose(DisposableTransformer.scheduler());
        return payDataObservable;
    }

    //*************************************************************************************************/

    /**
     * cash app pay by citcon
     **/
    static void prepareCashAppPayByCitcon(@NonNull CashAppPayBean bean, @NonNull PaymentHelper.OnPaymentCallback callback) {
        OrderApi loader = PaymentHelper.getLoader();
        if (loader == null) {
            PaymentHelper.dispatchResult(callback, false, bean.getCancelUrl());
            return;
        }

        Observable<ResponseBean<CitconPayBean>> payDataObservable = getCitconCashAppPayDataObservable(loader, bean.isCombinePay(), bean.getOrderIds(), bean.getAmount());

        Observable<ResponseBean<CitconTokenBean>> citconTokenObservable = loader.getCashAppCitconToken()
                .compose(ResponseTransformer.scheduler());

        Observable.merge(citconTokenObservable, payDataObservable)
                .subscribe(getCitconPayDataObserver(Constants.PayType.CASH_APP, bean.isCombinePay(), bean.getOrderIds(), callback));
    }

    private static Observable<ResponseBean<CitconPayBean>> getCitconCashAppPayDataObservable(
            OrderApi loader,
            boolean isCombinePay,
            @NonNull List<Integer> orderIds,
            double amount
    ) {
        RequestParams params = new RequestParams()
                .put("amount", amount)
                .put("currency", "USD")
                .put("version", "V1")
                .put("platform", "app");
        Observable<ResponseBean<CitconPayBean>> payDataObservable;
        payDataObservable = loader.getCashAppPayDataByCitcon(params.putNonNull("order_ids", (Serializable) orderIds).get());
        payDataObservable = payDataObservable.compose(DisposableTransformer.scheduler());
        return payDataObservable;
    }

    private static ResponseObserver<ResponseBean<?>> getCitconPayDataObserver(
            String payType,
            boolean isCombinePay,
            @NonNull List<Integer> orderIds,
            @NonNull PaymentHelper.OnPaymentCallback callback
    ) {
        return new ResponseObserver<ResponseBean<?>>() {

            CitconPayBean payBean = null;
            CitconTokenBean tokenBean = null;

            @Override
            public void onError(FailureBean failure) {
                PaymentHelper.dispatchError(callback, failure);
            }

            @Override
            public void onResponse(ResponseBean<?> response) {
                Object data = response.getData();
                if (data instanceof CitconPayBean) {
                    payBean = (CitconPayBean) data;
                } else if (data instanceof CitconTokenBean) {
                    tokenBean = (CitconTokenBean) data;
                }
            }

            @Override
            public void onFinish() {
                super.onFinish();
                if (tokenBean != null && payBean != null) {
                    prepareCitconData(tokenBean);
                    requestCitconOrder(payType, isCombinePay, orderIds, payBean, callback);
                }
            }
        };
    }

    private static void requestCitconOrder(
            String payType,
            boolean isCombinePay,
            @NonNull List<Integer> orderIds,
            @NonNull CitconPayBean data,
            @NonNull PaymentHelper.OnPaymentCallback callback
    ) {
        Activity activity = LifecycleProvider.get().getTopActivity();
        if (activity == null) {
            PaymentHelper.dispatchResult(callback, false, data.cancel_url);
            return;
        }

        CPayOrder.Builder builder = new CPayOrder.Builder()
                .setLaunchType(CPayLaunchType.OTHERS)
                .setReferenceId(EmptyUtils.orEmpty(data.tx_id))
                .setBody(EmptyUtils.orEmpty(data.body))
                .setSubject(EmptyUtils.orEmpty(data.subject))
                .setAmount(EmptyUtils.orEmpty(data.amount))
                .setCurrency(EmptyUtils.orEmpty(data.currency))
                .setVendor(EmptyUtils.orEmpty(data.vendor))
                .setIpnUrl(EmptyUtils.orEmpty(data.ipn_url))
                .setCallbackUrl(EmptyUtils.orEmpty(data.success_url))
                .setCallbackCancelUrl(EmptyUtils.orEmpty(data.cancel_url))
                .setCallbackFailUrl(EmptyUtils.orEmpty(data.cancel_url))
                .enableCNPayAcceleration(data.enable_CN_pay_acceleration)
                .setAllowDuplicate(data.allow_duplicate);
        Locale locale = null;
        if (!EmptyUtils.isEmpty(data.country)) {
            locale = new Locale("", data.country, "");
        }
        if (locale == null) {
            locale = Locale.US;
        }
        builder.setCountry(locale);
        if (Constants.PayType.CASH_APP.equals(payType)) {
            builder.setDeepLink("citcon://" + DevConfig.APPLICATION_ID + ".cpay.sdk");
        }
        CPayOrder order = builder.build();
        Logger.json("CitconPaymentHelper", "CPaySDK::requestOrder()");
        if (callback instanceof OnCashAppPaymentEventCallback) {
            ((OnCashAppPaymentEventCallback) callback).onCashAppReady(data);
        }
        CPaySDK.getInstance().requestOrder(activity, order, new OrderResponse<CPayOrderResult>() {
            @Override
            public void gotOrderResult(CPayOrderResult object) {
                Logger.json("CitconPaymentHelper", "CPaySDK::requestOrder::gotOrderResult()", object);
                if (Constants.PayType.CASH_APP.equals(payType)) {
                    // Cash app will check order status by itself
                    // Handle abnormal status code
                    if (object == null || !"success".equals(object.mStatus)) {
                        PaymentHelper.prepareCheckOrderStatus(isCombinePay, orderIds, data, callback);
                    }
                } else {
                    PaymentHelper.prepareCheckOrderStatus(isCombinePay, orderIds, data, callback);
                }
            }
        });
    }

    static void prepareCheckCashAppPayByCitcon(
            @NonNull CashAppPayBean bean,
            @NonNull CitconPayBean payBean,
            @NonNull PaymentHelper.OnPaymentCallback callback
    ) {
        Logger.json("CitconPaymentHelper", "CPaySDK::inquireOrderByRef()");
        CPaySDK.getInstance().inquireOrderByRef(payBean.tx_id, payBean.currency, payBean.vendor, payBean.enable_CN_pay_acceleration, new InquireResponse<CPayInquireResult>() {
            @Override
            public void gotInquireResult(CPayInquireResult cPayInquireResult) {
                PaymentHelper.prepareCheckOrderStatus(bean.isCombinePay(), bean.getOrderIds(), payBean, callback);
            }
        });
    }

    public interface OnCashAppPaymentEventCallback extends PaymentHelper.OnPaymentEventCallback {
        void onCashAppReady(CitconPayBean citconPayBean);
    }
}
