package com.sayweee.weee.service.helper;

import android.os.Build;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.sayweee.logger.Logger;
import com.sayweee.service.ConfigService;
import com.sayweee.weee.service.config.ConfigManager;
import com.sayweee.weee.service.config.bean.CdnTrafficPolicyBean;
import com.sayweee.weee.service.webp.ImageSpec;
import com.sayweee.weee.service.webp.WebpManager;
import com.sayweee.weee.utils.CollectionUtils;
import com.sayweee.weee.utils.function.Predicate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class CdnTrafficHelper {

    public static final boolean LOG_ENABLE = false;
    public static final boolean LOG_ENABLE_HTML = false;

    @SuppressWarnings("squid:S2245")
    private static final Random generator = new Random();

    private static final String REGEX_HTML_IMG_SRC = "https?://([^/]+)\\S+.(jpeg|jpg|png|JPEG|JPG|PNG)";
    private static final Pattern PATTERN_HTML_IMG_SRC = Pattern.compile(REGEX_HTML_IMG_SRC);

    private CdnTrafficHelper() {

    }

    /**
     * 根据图片的cdn配置，提前生成映射表
     * 映射表保存内存在config
     */
    public static Map<String, String> parseConfig(CdnTrafficPolicyBean config) {
        Map<String, String> map = new HashMap<>();
        if (config == null) {
            return map;
        }
        if (config.list != null && !config.list.isEmpty()) {
            fillCdnMapping(map, config.list);
        }
        Logger.enable(LOG_ENABLE).f("image").json("config", config);
        Logger.enable(LOG_ENABLE).f("image").json("mapping", map);
        return map;
    }

    private static void fillCdnMapping(@NonNull Map<String, String> map, @NonNull List<CdnTrafficPolicyBean.CdnPolicyBean> list) {
        for (CdnTrafficPolicyBean.CdnPolicyBean policyBean : list) {
            int random = generator.nextInt(100);
            if (policyBean != null && policyBean.currentHost != null && policyBean.cdnAlts != null && !policyBean.cdnAlts.isEmpty()) {
                CdnTrafficPolicyBean.CdnAltsBean target = CollectionUtils.firstOrNull(
                        policyBean.cdnAlts,
                        new Predicate<CdnTrafficPolicyBean.CdnAltsBean>() {
                            int count = 0;

                            @Override
                            public boolean test(CdnTrafficPolicyBean.CdnAltsBean cdnAltsBean) {
                                count += (int) (cdnAltsBean.rate * 100f);
                                return random < count;
                            }
                        }
                );
                if (target != null && target.host != null && !target.host.isEmpty()) {
                    map.put(policyBean.currentHost, target.host);
                }
            }
        }
    }

    /**
     * 根据cdn映射表生成新的url
     *
     * @param url 原图片链接
     * @return 替换后的图片链接
     */
    public static String transferUrl(String url) {
        Map<String, String> policy = ConfigService.get().getDynamicConfig(ConfigManager.DynamicConfig.CDN_TRAFFIC_POLICY);
        if (policy != null && !policy.isEmpty() && url != null && !url.isEmpty()) {
            int beginIndex = url.indexOf("://");
            if (beginIndex != -1) {
                beginIndex = beginIndex + 3;
                int endIndex = url.indexOf('/', beginIndex);
                if (endIndex != -1) {
                    String oldHost = url.substring(beginIndex, endIndex);
                    String newHost = policy.get(oldHost);
                    if (newHost != null && !newHost.isEmpty() && !newHost.equals(oldHost)) {
                        StringBuilder builder = new StringBuilder(url);
                        return builder.replace(beginIndex, endIndex, newHost).toString();
                    }
                }
            }
        }
        return url;
    }

    @NonNull
    public static String transferHtmlImgSrc(@Nullable String html) {
        String oldHtml = html != null ? html : "";
        Matcher matcher = PATTERN_HTML_IMG_SRC.matcher(html);

        boolean result = matcher.find();
        if (result) {
            StringBuffer sb = new StringBuffer(oldHtml.length() + 16);
            do {
                String oldSrc = matcher.group();
                oldSrc = WebpManager.get().getConvertUrl(true, ImageSpec.SPEC_375_AUTO, oldSrc);
                if (Build.VERSION.SDK_INT < Build.VERSION_CODES.O) {
                    int pos = oldSrc.lastIndexOf("webp");
                    if (pos > -1 && oldSrc.endsWith(WebpManager.WEBP_SPEC_SUFFIX_TWO)) {
                        oldSrc = oldSrc.substring(0, pos) + "png";
                    }
                }
                String newSrc = transferUrl(oldSrc);
                if (newSrc != null && !newSrc.isEmpty() && !newSrc.equals(oldSrc)) {
                    matcher.appendReplacement(sb, newSrc);
                } else {
                    matcher.appendReplacement(sb, oldSrc);
                }
                result = matcher.find();
            } while (result);
            matcher.appendTail(sb);

            String newHtml = sb.toString();
            if (CdnTrafficHelper.LOG_ENABLE_HTML && !oldHtml.equals(newHtml)) {
                Logger
                        .i("transferHtmlImgSrc",
                                "===== oldHtml =====\n" + oldHtml + "\n\n" +
                                        "===== newHtml =====\n" + newHtml + "\n\n"
                        );
            }
            return newHtml;
        }
        return oldHtml;
    }

}
