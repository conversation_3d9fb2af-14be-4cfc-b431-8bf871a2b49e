package com.sayweee.weee.service.helper;

import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.sayweee.logger.Logger;
import com.sayweee.scheduler.TaskScheduler;
import com.sayweee.service.ContextService;
import com.sayweee.service.SessionService;
import com.sayweee.weee.global.config.AppConfig;
import com.sayweee.weee.global.config.DevConfig;
import com.sayweee.weee.global.config.VariantConfig;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.global.manager.LanguageManager;
import com.sayweee.weee.global.manager.OrderManager;
import com.sayweee.weee.module.launch.service.StoreManager;
import com.sayweee.wrapper.helper.lifecycle.LifecycleProvider;

import java.io.IOException;
import java.util.TimeZone;

import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;

/**
 * Author:  winds
 * Email:   <EMAIL>
 * Date:    2020/6/1.
 * Desc:
 */
public final class HeadersInterceptor implements Interceptor {

    public static final String WEEE_TOKEN = "weee-token";
    public static final String WEEE_SESSION_TOKEN = "weee-session-token";
    public static final String AUTHORIZATION = "authorization";//new token
    public static final String LANG = "lang"; //语言
    public static final String SYSTEM_LANG = "system-lang"; //语言
    public static final String PLATFORM = "platform"; //平台标示
    public static final String VERSION = "version"; //接口版本
    public static final String APP_VERSION = "app_version"; //app版本
    public static final String USER_AGENT = "user-agent";
    public static final String DEVICE_ID = "device-id";
    public static final String STORE = "weee-store";
    @SuppressWarnings("unused")
    public static final String COOKIE = "Cookie";
    public static final String ZIPCODE = "zipcode";
    public static final String TIMEZONE = "timezone";
    public static final String D2 = "d2";
    public static final String ST = "st";
    public static final String WEEE_DATE = "Weee-Date";
    public static final String WEEE_ZIPCODE = "Weee-Zipcode";
    public static final String WEEE_SALES_ORG_ID = "weee-sales-org-id";
    public static final String X_AWS_WAF_TOKEN = "x-aws-waf-token";
    public static final String WEEE_CHANNEL = "weee-channel";

    public static final String VALUE_PLATFORM = "android";
    public static final String VALUE_VERSION = "v3";

    private volatile String defaultTimeZone = "";
    private volatile String agent = "";
    private volatile String systemUserAgent;

    private static class Builder {
        private static final HeadersInterceptor INSTANCE = new HeadersInterceptor();
    }

    public static HeadersInterceptor getInstance() {
        return Builder.INSTANCE;
    }

    private HeadersInterceptor() {
        getTimeZone();
    }

    private void getTimeZone() {
        TaskScheduler.enqueue(this::realGetTimeZone);
    }

    private void realGetTimeZone() {
        this.defaultTimeZone = TimeZone.getDefault().getID();
    }

    private static class RequestInfo {
        private String language = "";
        private String deviceId = "";
        private String agent = "";
        private String bearerToken = "";
        private String d2 = "";
        private String st = "";
    }

    private RequestInfo buildRequestInfo() {
        RequestInfo info = new RequestInfo();
        info.language = LanguageManager.get().getLanguage();
        try {
            info.deviceId = ContextService.get().getDeviceId(LifecycleProvider.get().getApplication());
        } catch (Exception e) {
            Logger.e(e);
        }

        info.agent = SessionService.get().getUserAgent();
        this.agent = info.agent;

        info.bearerToken = AccountManager.get().getBearerToken();

        info.d2 = ContextService.get().getFingerprintData();
        info.st = ContextService.get().getFingerprintSign();
        return info;
    }

    @NonNull
    @Override
    public Response intercept(Chain chain) throws IOException {
        RequestInfo info = buildRequestInfo();
        String sessionToken = AccountManager.get().getSessionToken();
        Request original = chain.request();
        Request.Builder builder = original.newBuilder()
                .header(USER_AGENT, info.agent)
                .header(LANG, info.language)
                .header(DEVICE_ID, info.deviceId)
                .header(TIMEZONE, defaultTimeZone)
                .header(SYSTEM_LANG, LanguageManager.get().getSystemLanguage())
                .header(PLATFORM, VALUE_PLATFORM)
                .header(VERSION, VALUE_VERSION)
                .header(APP_VERSION, DevConfig.VERSION_NAME)
                .header(WEEE_SESSION_TOKEN, sessionToken == null ? "" : sessionToken)
                .header(WEEE_CHANNEL, VariantConfig.WEEE_CHANNEL)
                .header(D2, info.d2)
                .header(ST, info.st);
        if (!TextUtils.isEmpty(info.bearerToken)) {
            builder.header(AUTHORIZATION, info.bearerToken); //新app的token
        }
        if (AppConfig.HOST_TAG != null) {
            builder.header("x-canary-tag", AppConfig.HOST_TAG);
        }
        builder.header(STORE, StoreManager.get().getStoreOnHeader());
        String zipcode = OrderManager.get().getZipCode();
        if (zipcode != null && !zipcode.isEmpty()) {
            builder.header(ZIPCODE, zipcode);
            builder.header(WEEE_ZIPCODE, zipcode);
        }
        String date = OrderManager.get().getDeliveryPickupDate();
        if (date != null && !date.isEmpty()) {
            builder.header(WEEE_DATE, date);
        }
        String saleOrgId = OrderManager.get().getSaleOrgId();
        if (saleOrgId != null && !saleOrgId.isEmpty()) {
            builder.header(WEEE_SALES_ORG_ID, saleOrgId);
        }
        String wafToken = ContextService.get().getWafToken();
        if (wafToken != null && !wafToken.isEmpty()) {
            builder.header(X_AWS_WAF_TOKEN, wafToken);
        }
        return chain.proceed(builder.method(original.method(), original.body()).build());
    }

    public String getAgentInfo() {
        ensureAgentInfo();
        return agent;
    }

    private void ensureAgentInfo() {
        if (agent == null || agent.isEmpty()) {
            buildRequestInfo();
        }
    }
}
