package com.sayweee.weee.service.hotfix;

import android.app.ActivityManager;
import android.content.Context;
import android.os.Build;
import android.webkit.WebView;

import com.sayweee.weee.global.config.DevConfig;


/**
 * Author:  winds
 * Email:   <EMAIL>
 * Date:    2021/4/12.
 * Desc:
 */
public class WebViewHotfix {

    public static void setWebDataSuffixFix(Context context) {
        try {
            String packageName = DevConfig.APPLICATION_ID;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                String processName = getProcessName(context);
                if (processName != null && !processName.equals(packageName)) {
                    WebView.setDataDirectorySuffix(processName);
                }
            }
        } catch (Exception ignored) {
        }
    }

    public static String getProcessName(Context context) {
        if (context != null) {
            ActivityManager manager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
            for (ActivityManager.RunningAppProcessInfo processInfo : manager.getRunningAppProcesses()) {
                if (processInfo.pid == android.os.Process.myPid()) {
                    return processInfo.processName;
                }
            }
        }
        return null;
    }
}
