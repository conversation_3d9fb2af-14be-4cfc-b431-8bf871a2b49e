package com.sayweee.weee.service.config;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.alibaba.fastjson.TypeReference;
import com.sayweee.service.ConfigService;
import com.sayweee.service.config.conf.bean.ConfigResult;
import com.sayweee.weee.global.config.DevConfig;
import com.sayweee.weee.global.config.VariantConfig;
import com.sayweee.weee.module.launch.bean.AppIconBean;
import com.sayweee.weee.module.web.FailbackHelper;
import com.sayweee.weee.module.web.bean.WebViewConfigBean;
import com.sayweee.weee.service.config.bean.AddressPositioningConfigBean;
import com.sayweee.weee.service.config.bean.BannerConfigBean;
import com.sayweee.weee.service.config.bean.CategoryConfigBean;
import com.sayweee.weee.service.config.bean.CdnTrafficPolicyBean;
import com.sayweee.weee.service.config.bean.ClarityConfigBean;
import com.sayweee.weee.service.config.bean.CommonConfigBean;
import com.sayweee.weee.service.config.bean.CommunityConfigBean;
import com.sayweee.weee.service.config.bean.ExperimentConfigBean;
import com.sayweee.weee.service.config.bean.FailbackConfigBean;
import com.sayweee.weee.service.config.bean.HomeConfigBean;
import com.sayweee.weee.service.config.bean.MonitorConfigBean;
import com.sayweee.weee.service.config.bean.ProductConfigBean;
import com.sayweee.weee.service.config.bean.SellerConfigBean;
import com.sayweee.weee.service.config.bean.ShortcutConfigBean;
import com.sayweee.weee.service.experiment.DynamicExperimentHelper;
import com.sayweee.weee.service.helper.CdnTrafficHelper;
import com.sayweee.weee.service.helper.MonitorHelper;
import com.sayweee.weee.service.shortcut.ShortcutHelper;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.utils.JsonUtils;
import com.sayweee.wrapper.bean.FailureBean;

import java.util.List;
import java.util.Map;

/**
 * Author:  winds
 * Date:    1/11/22.
 * Desc:
 */
public class ConfigManager {

    // dynamic config
    public interface DynamicConfig {
        String SHORTCUT = "shortcut";
        String COMMUNITY = "community";
        String CATEGORY = "category";
        String HOME = "home";
        String ADDRESS_POSITIONING = "address_positioning";
        String CLARITY = "clarity";
        String BRAND = "brand";
        String PRODUCT = "product";
        String MONITOR = "monitor";
        String BANNER = "banner";
        String FAILBACK_TO_H5 = "failback_to_h5_android-";
        String FAILBACK_TO_H5_PATTERNS = "failback_to_h5_patterns_android-";
        String CDN_TRAFFIC_POLICY = "cdn_traffic_policy";
        String SELLER = "seller";
        String WEBVIEW = "webview_android";
        String PDP = "pdp";
    }

    public static ConfigManager get() {
        return Builder.instance;
    }

    private static final class Builder {
        private static final ConfigManager instance = new ConfigManager();
    }

    public void fetchServiceConfig() {
        String configKeys = VariantConfig.CONFIG_APP_ICON_KEY;
        ConfigService.get().fetchServiceConfig(configKeys, null, this::parseConfig, null);
    }

    @Nullable
    private Object parseConfig(@NonNull String configKey, @Nullable String configValue) {
        if (EmptyUtils.isEmpty(configKey) || EmptyUtils.isEmpty(configValue)) {
            return null;
        }
        Object value;
        if (VariantConfig.CONFIG_APP_ICON_KEY.equals(configKey)) {
            value = JsonUtils.parseObject(configValue, new TypeReference<Map<String, AppIconBean>>() {
            });
        } else {
            value = configValue;
        }
        return value;
    }

    public void fetchDynamicConfig() {
        ConfigService.get().fetchDynamicConfig(
                /* force= */false,
                /* params= */null,
                /* parser= */this::parseDynamicConfig,
                /* callback= */this::handleDynamicConfigCallback
        );
    }

    private void handleDynamicConfigCallback(@Nullable ConfigResult result, @Nullable FailureBean failureBean) {
        if (result != null) {
            //apply fail back
            List<String> patterns = FailbackHelper.parsePattern();
            result.put(DynamicConfig.FAILBACK_TO_H5_PATTERNS, patterns);
            applyDynamicConfig();
        }
    }

    @Nullable
    private Object parseDynamicConfig(@NonNull String configKey, @Nullable String configValue) {
        if (EmptyUtils.isEmpty(configKey) || EmptyUtils.isEmpty(configValue)) {
            return null;
        }
        if (configKey.startsWith(DynamicConfig.FAILBACK_TO_H5)) {
            String targetVersion = configKey.substring(DynamicConfig.FAILBACK_TO_H5.length());
            int i = CommonTools.compareAppVersion(targetVersion, DevConfig.VERSION_NAME);
            if (i > 0) { //配置大于当前版本，配置生效 否则则不解析处理
                return JsonUtils.parseObject(configValue, FailbackConfigBean.class);
            }
            return null;
        }

        Object value;
        switch (configKey) {
            case DynamicConfig.SHORTCUT:
                Map<String, List<ShortcutConfigBean.ShortcutItemBean>> map;
                map = JsonUtils.parseObject(configValue, new TypeReference<Map<String, List<ShortcutConfigBean.ShortcutItemBean>>>() {
                });
                if (map != null && !map.isEmpty()) {
                    ShortcutConfigBean bean = new ShortcutConfigBean();
                    bean.data = map;
                    value = bean;
                } else {
                    value = null;
                }
                break;
            case DynamicConfig.COMMUNITY:
                value = JsonUtils.parseObject(configValue, CommunityConfigBean.class);
                break;
            case DynamicConfig.CATEGORY:
                value = JsonUtils.parseObject(configValue, CategoryConfigBean.class);
                break;
            case DynamicConfig.ADDRESS_POSITIONING:
                value = JsonUtils.parseObject(configValue, AddressPositioningConfigBean.class);
                break;
            case DynamicExperimentHelper.Key.KEY_XP_SEARCH_BAR_BUTTON_VISIBLE:
                value = JsonUtils.parseObject(configValue, ExperimentConfigBean.class);
                break;
            case DynamicConfig.HOME:
                value = JsonUtils.parseObject(configValue, HomeConfigBean.class);
                break;
            case DynamicConfig.CLARITY:
                value = JsonUtils.parseObject(configValue, ClarityConfigBean.class);
                break;
            case DynamicConfig.BRAND:
                value = JsonUtils.parseObject(configValue, CommonConfigBean.class);
                break;
            case DynamicConfig.PRODUCT:
                value = JsonUtils.parseObject(configValue, ProductConfigBean.class);
                break;
            case DynamicConfig.MONITOR:
                value = JsonUtils.parseObject(configValue, MonitorConfigBean.class);
                break;
            case DynamicConfig.BANNER:
                value = JsonUtils.parseObject(configValue, BannerConfigBean.class);
                break;
            case DynamicConfig.CDN_TRAFFIC_POLICY:
                CdnTrafficPolicyBean cdnTrafficPolicyBean = JsonUtils.parseObject(configValue, CdnTrafficPolicyBean.class);
                value = CdnTrafficHelper.parseConfig(cdnTrafficPolicyBean);
                break;
            case DynamicConfig.SELLER:
                value = JsonUtils.parseObject(configValue, SellerConfigBean.class);
                break;
            case DynamicConfig.WEBVIEW:
                value = JsonUtils.parseObject(configValue, WebViewConfigBean.class);
                break;
            default:
                value = null;
                break;
        }
        return value;
    }

    protected void applyDynamicConfig() {
        ShortcutHelper.applyShortcut();
        MonitorHelper.applyConfig();
    }
}
