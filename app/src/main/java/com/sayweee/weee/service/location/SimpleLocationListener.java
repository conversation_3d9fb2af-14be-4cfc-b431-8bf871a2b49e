package com.sayweee.weee.service.location;

import android.location.Location;
import android.location.LocationListener;
import android.os.Bundle;

import com.sayweee.logger.Logger;

/**
 * Author:  winds
 * Email:   <EMAIL>
 * Date:    2020/10/29.
 * Desc:
 */
public abstract class SimpleLocationListener implements LocationListener {

    @Override
    public void onLocationChanged(Location location) {
        Logger.json(location);
        if(location != null) {
            onLocationSuccess(location);
            GeoInfoManager.get().setGeoInfo(location);
        }else{
            onLocationFailed(new NullPointerException("location failed"));
        }
    }

    @Override
    public void onStatusChanged(String provider, int status, Bundle extras) {
        Logger.json(provider, status, extras);
    }

    @Override
    public void onProviderEnabled(String provider) {
        Logger.json(provider);
    }

    @Override
    public void onProviderDisabled(String provider) {
        Logger.json(provider);
    }

    public abstract void onLocationSuccess(Location location);

    public void onLocationFailed(Exception e){
        Logger.e(e);
    }

    public void onLocationTimeout(){

    }
}
