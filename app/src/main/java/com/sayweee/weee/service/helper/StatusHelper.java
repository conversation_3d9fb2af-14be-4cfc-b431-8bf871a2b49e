package com.sayweee.weee.service.helper;

import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.sayweee.weee.R;
import com.sayweee.wrapper.bean.FailureBean;
import com.sayweee.wrapper.helper.lifecycle.LifecycleProvider;
import com.sayweee.wrapper.http.ExceptionHandler;
import com.sayweee.wrapper.http.support.Utils;

import me.winds.wrapper.status.DefaultStatusProvider;
import me.winds.wrapper.status.OnDefaultStatusListener;
import me.winds.wrapper.status.StatusManager;

/**
 * Author:  winds
 * Date:    2021/7/9.
 * Desc:
 */
public class StatusHelper {

    /**
     * 展示error状态
     *
     * @param manager
     * @param bean
     * @param view
     */
    public static boolean showStatus(StatusManager manager, FailureBean bean, RecyclerView view, View.OnClickListener listener) {
        RecyclerView.Adapter adapter = view != null ? view.getAdapter() : null;
        return showStatus(manager, bean, adapter == null || adapter.getItemCount() <= 0, listener);
    }

    public static boolean showStatus(StatusManager manager, FailureBean bean, boolean showStatus, View.OnClickListener listener) {
        return showStatus(manager, bean, showStatus, false, listener);
    }

    public static boolean showStatus(StatusManager manager, FailureBean bean, boolean showStatus, boolean useBackground, View.OnClickListener listener) {
        boolean show = manager != null && showStatus;
        boolean isNetworkConnected = Utils.isNetworkConnected(LifecycleProvider.get().getApplication());
        int resource = isNetworkConnected ? R.layout.layout_status_load_failed : R.layout.layout_status_no_network;
        if (show) {
            manager.show(new DefaultStatusProvider.DefaultNoNetWorkStatusProvider() {
                @Override
                public View getStatusView(ViewGroup containerView) {
                    return View.inflate(containerView.getContext(), resource, null);
                }
            }, new OnDefaultStatusListener() {
                @Override
                public void onStatusViewShow(String status, View statusView) {
                    if (statusView == null || statusView.getContext() == null) {
                        return;
                    }
                    if (useBackground) {
                        statusView.setBackgroundColor(ContextCompat.getColor(statusView.getContext(), R.color.color_back));
                    }
                    boolean connectError = ExceptionHandler.isConnectError(bean != null ? bean.getErrorCode() : ExceptionHandler.ERROR_UNKNOWN);
                    View tvRetry = statusView.findViewById(R.id.tv_retry);
                    if (tvRetry != null) {
                        tvRetry.setOnClickListener(listener);
                    }
                }
            });
        }
        return show;
    }

}
