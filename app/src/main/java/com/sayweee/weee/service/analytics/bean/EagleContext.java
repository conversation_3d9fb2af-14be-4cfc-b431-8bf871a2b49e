package com.sayweee.weee.service.analytics.bean;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.collection.ArrayMap;

import java.util.Map;

public class EagleContext implements IEagleTraceMap {

    private String filterSubCategory;
    private String catalogueNum;
    private String sort;
    private Map<String, String> filters;
    private String pageTarget;
    private String pageTab;
    private String brandName;
    private String globalVendor;
    private String traceId;
    private String status;
    private String tagId;
    private String tagKey;
    private String tagName;
    private String bannerName;
    private String relatedInfo;
    private String diffPrice;
    private String diffPriceUpsell;
    private String couponScope;
    private String couponCode;
    private Boolean volumePriceSupport;

    public EagleContext() {

    }

    public EagleContext(@NonNull EagleContext other) {
        this.filterSubCategory = other.filterSubCategory;
        this.catalogueNum = other.catalogueNum;
        this.sort = other.sort;
        this.filters = other.filters;
        this.pageTarget = other.pageTarget;
        this.pageTab = other.pageTab;
        this.brandName = other.brandName;
        this.globalVendor = other.globalVendor;
        this.traceId = other.traceId;
        this.status = other.status;
        this.tagId = other.tagId;
        this.tagKey = other.tagKey;
        this.tagName = other.tagName;
        this.bannerName = other.bannerName;
        this.relatedInfo = other.relatedInfo;
        this.diffPrice = other.diffPrice;
        this.diffPriceUpsell = other.diffPriceUpsell;
        this.couponScope = other.couponScope;
        this.couponCode = other.couponCode;
        this.volumePriceSupport = other.volumePriceSupport;
    }

    public EagleContext setFilterSubCategory(String filterSubCategory) {
        this.filterSubCategory = filterSubCategory;
        return this;
    }

    public EagleContext setCatalogueNum(String catalogueNum) {
        this.catalogueNum = catalogueNum;
        return this;
    }

    public EagleContext setSort(String sort) {
        this.sort = sort;
        return this;
    }

    public EagleContext setFilters(Map<String, String> filters) {
        this.filters = filters;
        return this;
    }

    public EagleContext setPageTarget(String pageTarget) {
        this.pageTarget = pageTarget;
        return this;
    }

    public EagleContext setPageTab(String pageTab) {
        this.pageTab = pageTab;
        return this;
    }

    public EagleContext setBrandName(String brandName) {
        this.brandName = brandName;
        return this;
    }

    public EagleContext setBannerName(String bannerName) {
        this.bannerName = bannerName;
        return this;
    }

    public EagleContext setGlobalVendor(String globalVendor) {
        this.globalVendor = globalVendor;
        return this;
    }

    public EagleContext setTraceId(String traceId) {
        this.traceId = traceId;
        return this;
    }

    public EagleContext setStatus(String status) {
        this.status = status;
        return this;
    }

    public EagleContext setTagId(String tagId) {
        this.tagId = tagId;
        return this;
    }

    public EagleContext setTagKey(String tagKey) {
        this.tagKey = tagKey;
        return this;
    }

    public EagleContext setTagName(String tagName) {
        this.tagName = tagName;
        return this;
    }

    public EagleContext setRelatedInfo(String relatedInfo) {
        this.relatedInfo = relatedInfo;
        return this;
    }

    public EagleContext setDiffPrice(String diffPrice) {
        this.diffPrice = diffPrice;
        return this;
    }

    public EagleContext setDiffPriceUpsell(String diffPriceUpsell) {
        this.diffPriceUpsell = diffPriceUpsell;
        return this;
    }

    public EagleContext setCouponScope(String couponScope) {
        this.couponScope = couponScope;
        return this;
    }

    public EagleContext setCouponCode(String couponCode) {
        this.couponCode = couponCode;
        return this;
    }

    public EagleContext setVolumePriceSupport(boolean volumePriceSupport) {
        this.volumePriceSupport = volumePriceSupport;
        return this;
    }

    public EagleContext putAll(
            String filterSubCategory,
            String catalogueNum,
            String sort,
            Map<String, String> filters,
            String pageTarget,
            String pageTab,
            String brandName,
            String globalVendor,
            String traceId
    ) {
        this.filterSubCategory = filterSubCategory;
        this.catalogueNum = catalogueNum;
        this.sort = sort;
        this.filters = filters;
        this.pageTarget = pageTarget;
        this.pageTab = pageTab;
        this.brandName = brandName;
        this.globalVendor = globalVendor;
        this.traceId = traceId;
        return this;
    }

    @Nullable
    @Override
    public Map<String, Object> asMap() {
        Map<String, Object> map = new ArrayMap<>(20);
        map.put("filter_sub_category", filterSubCategory);
        map.put("catalogue_num", catalogueNum);
        map.put("sort", sort);
        map.put("filters", filters);
        map.put("page_target", pageTarget);
        map.put("page_tab", pageTab);
        map.put("brand_name", brandName);
        map.put("global_vendor", globalVendor);
        map.put("trace_id", traceId);
        map.put("status", status);
        map.put("tag_id", tagId);
        map.put("tag_key", tagKey);
        map.put("tag_name", tagName);
        map.put("banner_nm", bannerName);
        map.put("related_info", relatedInfo);
        map.put("diff_price", diffPrice);
        map.put("diff_price_upsell", diffPriceUpsell);
        map.put("coupon_scope", couponScope);
        map.put("coupon_code", couponCode);
        if (volumePriceSupport != null) {
            map.put("volume_price_support", volumePriceSupport);
        }
        return map;
    }
}
