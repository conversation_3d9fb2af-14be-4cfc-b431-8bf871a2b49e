package com.sayweee.weee.service.guide;

import android.app.Activity;
import android.graphics.drawable.Drawable;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;

import com.app.hubert.guide.NewbieGuide;
import com.app.hubert.guide.core.Controller;
import com.app.hubert.guide.listener.OnLayoutInflatedListener;
import com.app.hubert.guide.model.GuidePage;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.request.target.DrawableImageViewTarget;
import com.bumptech.glide.request.transition.Transition;
import com.sayweee.weee.R;
import com.sayweee.weee.global.manager.AccountManager;
import com.sayweee.weee.global.manager.ImageLoader;
import com.sayweee.weee.utils.CommonTools;

/**
 * Author:  winds
 * Date:    2023/7/6.
 * Desc:
 */
public class GuideHelper {

    private GuideHelper(){}

    /**
     * 封面選擇引導
     *
     * @param activity
     */
    public static void showSelectCoverGuide(Activity activity, String path) {
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) return;
        AccountManager.get().setSelectCoverGuided(true);

        final int topPadding = CommonTools.getStatusBarHeight(activity)
                + activity.getResources().getDimensionPixelOffset(R.dimen.default_title_height)
                + CommonTools.dp2px(8);
        OnLayoutInflatedListener onLayoutInflatedListener = new OnLayoutInflatedListener() {
            @Override
            public void onLayoutInflated(View view, Controller controller) {
                view.setPadding(
                        CommonTools.dp2px(20),
                        topPadding,
                        CommonTools.dp2px(20),
                        0
                );
                ImageView ivCover = view.findViewById(R.id.iv_cover);
                LinearLayout llSelectCover = view.findViewById(R.id.ll_select_cover);
                ConstraintLayout layout_cover = view.findViewById(R.id.layout_cover);
                layout_cover.setVisibility(View.VISIBLE);
                ImageLoader.load(activity, path, new RequestOptions(), new DrawableImageViewTarget(ivCover) {
                    @Override
                    public void onResourceReady(@NonNull Drawable resource, @Nullable Transition<? super Drawable> transition) {
                        super.onResourceReady(resource, transition);
                        int m168 = CommonTools.dp2px(168);
                        int m128 = CommonTools.dp2px(128);
                        ViewGroup.LayoutParams layoutParams = ivCover.getLayoutParams();
                        if (layoutParams instanceof ConstraintLayout.LayoutParams) {
                            ConstraintLayout.LayoutParams params = (ConstraintLayout.LayoutParams) layoutParams;
                            params.width = resource.getIntrinsicWidth() > resource.getIntrinsicHeight() ? m168 : m128;
                            params.height = resource.getIntrinsicWidth() > resource.getIntrinsicHeight() ? m128 : m168;
                            ivCover.setLayoutParams(params);
                            ConstraintLayout.LayoutParams linearCoverParams = (ConstraintLayout.LayoutParams) llSelectCover.getLayoutParams();
                            linearCoverParams.width = resource.getIntrinsicWidth() > resource.getIntrinsicHeight() ? m168 : m128;
                            linearCoverParams.height = CommonTools.dp2px(30);
                            llSelectCover.setLayoutParams(linearCoverParams);
                        }
                    }
                });
            }
        };

        NewbieGuide.with(activity)
                .setLabel(AccountManager.KEY_SELECT_COVER_GUIDE)
                .alwaysShow(false)
                .addGuidePage(
                        GuidePage.newInstance()
                                .setEverywhereCancelable(false)
                                .setBackgroundColor(ContextCompat.getColor(activity, R.color.root_color_black_tint_11))
                                .setLayoutRes(R.layout.layout_select_cover_guide, R.id.tv_got_it)
                                .setOnLayoutInflatedListener(onLayoutInflatedListener)
                )
                .show();
    }

}
