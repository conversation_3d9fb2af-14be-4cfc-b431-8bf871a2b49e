package com.sayweee.weee.service.upload;

import com.sayweee.wrapper.http.RetrofitIml;
import okhttp3.OkHttpClient;
import retrofit2.Retrofit;

import javax.net.SocketFactory;
import java.io.IOException;
import java.net.InetAddress;
import java.net.Socket;
import java.net.UnknownHostException;

class RetrofitUploadImpl extends RetrofitIml {

    public static RetrofitUploadImpl get() {
        return RetrofitUploadImpl.Builder.instance;
    }

    public static final class Builder {
        private static final RetrofitUploadImpl instance = new RetrofitUploadImpl();
    }

    @Override
    public RetrofitUploadImpl initHttp(
            Retrofit retrofit
    ) {
        OkHttpClient client = (OkHttpClient) retrofit.callFactory();
        OkHttpClient.Builder clientBuilder = client.newBuilder();
        clientBuilder.socketFactory(new ProgressFriendlySocketFactory());

        Retrofit.Builder retrofitBuilder = retrofit.newBuilder();
        retrofitBuilder.callFactory(clientBuilder.build());
        this.retrofit = retrofitBuilder.build();
        return this;
    }

    private static class ProgressFriendlySocketFactory extends SocketFactory {

        private static final int DEFAULT_BUFFER_SIZE = 2048;

        @Override
        public Socket createSocket() throws IOException {
            return setSendBufferSize(new Socket());
        }

        @Override
        public Socket createSocket(String host, int port) throws IOException, UnknownHostException {
            return setSendBufferSize(new Socket(host, port));
        }

        @Override
        public Socket createSocket(String host, int port, InetAddress localHost, int localPort) throws IOException, UnknownHostException {
            return setSendBufferSize(new Socket(host, port, localHost, localPort));
        }

        @Override
        public Socket createSocket(InetAddress host, int port) throws IOException {
            return setSendBufferSize(new Socket(host, port));
        }

        @Override
        public Socket createSocket(InetAddress address, int port, InetAddress localAddress, int localPort) throws IOException {
            return setSendBufferSize(new Socket(address, port, localAddress, localPort));
        }

        private Socket setSendBufferSize(Socket socket) throws IOException {
            socket.setSendBufferSize(DEFAULT_BUFFER_SIZE);
            return socket;
        }
    }
}
