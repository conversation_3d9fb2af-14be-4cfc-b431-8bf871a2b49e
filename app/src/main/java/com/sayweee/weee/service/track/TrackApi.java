package com.sayweee.weee.service.track;

import com.sayweee.wrapper.bean.ResponseBean;

import io.reactivex.Observable;
import okhttp3.RequestBody;
import retrofit2.http.Body;
import retrofit2.http.Header;
import retrofit2.http.POST;

/**
 * Author:  winds
 * Email:   <EMAIL>
 * Date:    2020/10/20.
 * Desc:
 */
public interface TrackApi {

    /**
     * 批量记录用户行为
     */
    @POST("ec/customer/event/track")
    Observable<ResponseBean> trackEvent(@Header("weee_token") String weee_token, @Body RequestBody info);

    /**
     * 新埋点接口
     */
    @POST("bi/events/track")
    Observable<ResponseBean> trackEventNew(@Header("Data-Version") int dataVersion, @Body RequestBody info);
}
