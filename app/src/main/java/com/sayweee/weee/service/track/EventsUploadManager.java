package com.sayweee.weee.service.track;

import android.app.Activity;
import android.app.Application;

import androidx.annotation.NonNull;

import com.sayweee.logger.Logger;
import com.sayweee.weee.global.config.DevConfig;
import com.sayweee.weee.utils.DebugTimer;
import com.sayweee.wrapper.helper.lifecycle.SimpleLifecycleCallbacks;

import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

public class EventsUploadManager {
    static final boolean DEBUG_VERBOSE = DevConfig.isDebug();

    private static final String TAG = "EventsUploadManager";

    private static final long DEFAULT_INTERVAL = 2000;

    private volatile ScheduledExecutorService taskScheduler;
    private SimpleLifecycleCallbacks simpleLifecycleCallbacks;

    private final Object producersLock = new Object();
    private Set<Producer> producers;

    private boolean hasConsumeTasksScheduled;
    private Long lastConsumeStartTs;

    public static abstract class Producer {
        boolean released = false;
        boolean attachedToConsumer = false;

        public Producer() {
            attachToEventsUploadManager();
        }

        public abstract String getId();
        public abstract void processTasks();
        public abstract boolean hasPendingTasks();
        public abstract int getPendingTasksSize();

        public void attachToEventsUploadManager() {
            EventsUploadManager.get().attachProducerAsync(this);
        }

        public void onAttachedToConsumer() {
            if (hasPendingTasks()) {
                startConsumingTasks();
            }
        }

        public void onEnqueueEvent() {
            startConsumingTasks();

            if (DEBUG_VERBOSE) {
                Logger.d(TAG, "Producer:" + getId() + " onEnqueueEvent hasPendingTasks:" + hasPendingTasks() + " size:" + getPendingTasksSize());
            }
        }

        public void startConsumingTasks() {
            getUploadEventsManager().startConsuming();

            if (DEBUG_VERBOSE) {
                Logger.d(TAG, "Producer:" + getId() + " startConsumingTasks hasPendingTasks:" + hasPendingTasks() + " size:" + getPendingTasksSize());
            }
        }

        public EventsUploadManager getUploadEventsManager() {
            return EventsUploadManager.get();
        }

        public void release() {
            if (this.released) return;
            if (DEBUG_VERBOSE) {
                Logger.d(TAG, "Producer:" + getId() + " release hasPendingTasks:" + hasPendingTasks() + " size:" + getPendingTasksSize());
            }

            this.released = true;
            if (hasPendingTasks()) {
                getUploadEventsManager().startConsuming(0L, true);
            }
            else {
                getUploadEventsManager().shutdownScheduler();
            }
        }

        public boolean isReleased() {
            return released;
        }

        @Override
        public String toString() {
            return "Producer{" +
                    "id=" + getId() +
                    "hasPendingTasks=" + hasPendingTasks() +
                    " released=" + released +
                    '}';
        }
    }

    private static class Builder {
        private static final EventsUploadManager INSTANCE = new EventsUploadManager();
    }

    public static EventsUploadManager get() {
        return Builder.INSTANCE;
    }

    private EventsUploadManager() {

    }

    public void attach(Application application) {
        if (application != null) {
            try {
                SimpleLifecycleCallbacks callbacks = getSimpleLifecycleCallbacks();
                application.unregisterActivityLifecycleCallbacks(callbacks);
                application.registerActivityLifecycleCallbacks(callbacks);
            } catch (Throwable e) {
                if (DEBUG_VERBOSE) {
                    Logger.e(TAG, e);
                }
            }
        }
    }

    private SimpleLifecycleCallbacks getSimpleLifecycleCallbacks() {
        if (simpleLifecycleCallbacks == null) {
            simpleLifecycleCallbacks = new SimpleLifecycleCallbacks() {
                int activityCount;
                @Override
                public void onActivityStarted(@NonNull Activity activity) {
                    activityCount++;
                }

                @Override
                public void onActivityStopped(@NonNull Activity activity) {
                    if (--activityCount <= 0) {
                        activityCount = 0;
                        flushAll();
                    }
                }
            };
        }
        return simpleLifecycleCallbacks;
    }

    public Set<Producer> getProducers() {
        if (producers == null) {
            producers = new HashSet<>();
        }
        return producers;
    }

    public void attachProducerAsync(Producer producer) {
        try {
            if (producer != null) {
                getTaskScheduler().submit(() -> {
                    try {
                        synchronized (producersLock) {
                            getProducers().add(producer);
                        }

                        producer.attachedToConsumer = true;
                        producer.onAttachedToConsumer();
                    } catch (Exception e) {
                        if (DEBUG_VERBOSE) {
                            Logger.e(TAG, e);
                        }
                    } finally {
                        if (DEBUG_VERBOSE) {
                            Logger.d(TAG, "attachProducer producer:" + producer);
                        }
                    }
                });
            }
        }
        catch (Exception e) {
            if (DEBUG_VERBOSE) {
                Logger.e(TAG, e);
            }
        }
    }

    public void detachProducer(Producer producer) {
        try {
            if (producer != null && producers != null && producers.contains(producer)) {
                getProducers().remove(producer);
            }
        } catch (Exception e) {
            if (DEBUG_VERBOSE) {
                Logger.e(TAG, e);
            }
        }
        finally {
            if (DEBUG_VERBOSE) {
                Logger.d(TAG, "detachProducer producer:" + producer);
            }
        }
    }

    public void flushAll() {
        startConsumingNow();
    }

    public void startConsumingNow() {
        startConsuming(0L, false);
    }

    public void startConsuming() {
        this.startConsuming(DEFAULT_INTERVAL, false);
    }

    public ScheduledExecutorService getTaskScheduler() {
        if (this.taskScheduler == null) {
            synchronized (this) {
                if (this.taskScheduler == null) {
                    this.taskScheduler = Executors.newSingleThreadScheduledExecutor();
                }
            }
        }
        return this.taskScheduler;
    }

    public void startConsuming(long intervalInMs, boolean releaseOnFinish) {
        if (hasConsumeTasksScheduled && !releaseOnFinish) {
            if (DEBUG_VERBOSE) {
                Logger.d(TAG, "startConsuming SKIP");
            }
            return;
        }

        this.hasConsumeTasksScheduled = true;

        final Long currentTs = System.currentTimeMillis();
        this.lastConsumeStartTs = currentTs;

        getTaskScheduler().schedule(() -> {
            Set<Producer> releasedProducers = null;
            try {
                if (!currentTs.equals(this.lastConsumeStartTs)) {
                    Logger.d(TAG, "startConsuming SKIP currentTs:" + currentTs + " lastConsumeStartTs:" + this.lastConsumeStartTs);
                    return;
                }

                if (producers == null || producers.isEmpty()) {
                    return;
                }

                AtomicBoolean hasMoreTasks = new AtomicBoolean();
                do {
                    hasMoreTasks.set(false);

                    Set<Producer> producersCopy;
                    synchronized (producersLock) {
                        producersCopy = new HashSet<>(producers);
                    }

                    for (Producer producer : producersCopy) {
                        if (producer != null) {
                            try {
                                consumeTasksForProducer(producer, hasMoreTasks);
                            } catch (Throwable e) {
                                if (DEBUG_VERBOSE) {
                                    Logger.e(TAG, e);
                                }
                            } finally {
                                if (producer.isReleased()) {
                                    if (releasedProducers == null) {
                                        releasedProducers = new HashSet<>();
                                    }
                                    releasedProducers.add(producer);
                                }

                                if (DEBUG_VERBOSE) {
                                    Logger.d(TAG, "startConsuming consumed tasks for producer:" + producer.getId() + " isReleased:" + producer.isReleased());
                                }
                            }
                        }
                    }
                }
                while (hasMoreTasks.get());
            }
            catch (Exception e) {
                if (DEBUG_VERBOSE) {
                    Logger.e(TAG, e);
                }
            }
            finally {
                if (releasedProducers != null) {
                    try {
                        synchronized (producersLock) {
                            for (Producer p : releasedProducers) {
                                detachProducer(p);
                            }
                        }
                    } catch (Throwable e) {
                        if (DEBUG_VERBOSE) {
                            Logger.e(TAG, e);
                        }
                    }
                }

                if (DEBUG_VERBOSE) {
                    Logger.d(TAG, "startConsuming taskScheduler DONE");
                }

                this.hasConsumeTasksScheduled = false;

                if (releaseOnFinish) {
                    shutdownScheduler();
                }
            }
        }, intervalInMs, TimeUnit.MILLISECONDS);

        if (DEBUG_VERBOSE) {
            Logger.d(TAG, "startConsuming ENQUEUED");
        }
    }

    private void consumeTasksForProducer(Producer producer, AtomicBoolean hasMoreTasks) {
        DebugTimer debugTimer = DEBUG_VERBOSE ? new DebugTimer() : null;
        try {
            int tasksSize = producer.getPendingTasksSize();
            if (debugTimer != null) debugTimer.mark("tasksSize:" + tasksSize);

            if (producer.hasPendingTasks()) {
                producer.processTasks();
                if (debugTimer != null) {
                    debugTimer.mark("processTask");
                }
            }
        }
        catch (Exception e) {
            if (debugTimer != null) {
                debugTimer.mark("processTaskError-" + e.getMessage());
            }
            if (DEBUG_VERBOSE) {
                Logger.e(TAG, e);
            }
        }
        finally {
            if (!hasMoreTasks.get()) {
                boolean hasPendingTasks = producer.hasPendingTasks();
                if (hasPendingTasks) {
                    hasMoreTasks.set(true);
                }
                if (debugTimer != null) {
                    debugTimer.mark("hasTasks-" + hasPendingTasks);
                }
            }
            if (DEBUG_VERBOSE) {
                Logger.d(TAG, "consumeTasksForProducer DONE producer:" + producer.getId() + " hasMoreTasks:" + hasMoreTasks.get() + " timer:" + debugTimer);
            }
        }
    }

    private void shutdownScheduler() {
        if (taskScheduler != null && !taskScheduler.isShutdown()) {
            try {
                taskScheduler.shutdown();
            } catch (Exception e) {
                Logger.e(TAG, e);
            } finally {
                this.taskScheduler = null;
                if (DEBUG_VERBOSE) {
                    Logger.d(TAG, "release");
                }
            }
        }
    }
}
