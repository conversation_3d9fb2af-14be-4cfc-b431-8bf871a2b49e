package com.sayweee.weee.service.analytics.utils;

import android.app.Activity;
import android.content.Context;
import android.content.res.Resources;
import android.provider.Settings;
import android.view.inputmethod.InputMethodInfo;
import android.view.inputmethod.InputMethodManager;
import android.view.inputmethod.InputMethodSubtype;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.os.ConfigurationCompat;
import androidx.core.os.LocaleListCompat;

import com.sayweee.logger.Logger;
import com.sayweee.weee.global.manager.LanguageManager;
import com.sayweee.weee.service.analytics.AppAnalytics;
import com.sayweee.weee.service.analytics.EagleTrackEvent;
import com.sayweee.weee.service.analytics.EagleTrackModel;
import com.sayweee.weee.service.track.TrackParams;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.wrapper.helper.lifecycle.LifecycleProvider;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

/**
 * Author:  winds
 * Date:    2022/6/21.
 * Desc:
 */
public class KeyboardTrackHelper {

    public static boolean isTracked;

    public static void trackKeyboardInfo() {
        if (isTracked) {
            return;
        }
        Activity context = LifecycleProvider.get().getTopActivity();
        if (context != null) {
            isTracked = true;
            InputMethodManager imm = (InputMethodManager) context.getSystemService(Context.INPUT_METHOD_SERVICE);
            List<InputMethodInfo> list = getEnabledInputMethodList(imm);
            if (!EmptyUtils.isEmpty(list)) {
                for (InputMethodInfo info : list) {
                    List<InputMethodSubtype> subtypes = imm.getEnabledInputMethodSubtypeList(info, true);
                    if (!EmptyUtils.isEmpty(subtypes)) {
                        StringBuilder builder = new StringBuilder();
                        for (InputMethodSubtype subtype : subtypes) {
                            if (subtype != null) {
                                if (builder.length() > 0) {
                                    builder.append(",");
                                }
                                String locale = subtype.getLocale();
                                builder.append(locale);
                            }
                        }
                        if (builder.length() > 0) {
                            String language = builder.toString();
                            AppAnalytics.logEvent(EagleTrackEvent.EventType.KEYBOARD_INPUT, new EagleTrackModel.Builder()
                                    .addContent(new TrackParams().put("language", language).get())
                                    .build().getParams());
                        }
                    }
                }
            }
        }
    }

    public static String getKeyboardFirstLanguage() {
        Activity context = LifecycleProvider.get().getTopActivity();
        if (context != null) {
            InputMethodManager imm = (InputMethodManager) context.getSystemService(Context.INPUT_METHOD_SERVICE);
            List<InputMethodInfo> list = getEnabledInputMethodList(imm);
            if (!EmptyUtils.isEmpty(list)) {
                for (InputMethodInfo info : list) {
                    List<InputMethodSubtype> subtypes = imm.getEnabledInputMethodSubtypeList(info, true);
                    if (!EmptyUtils.isEmpty(subtypes)) {
                        for (InputMethodSubtype subtype : subtypes) {
                            if (subtype != null) {
                                //获取输入法语言是否为支持语言
                                String locale = subtype.getLocale();
                                if (!EmptyUtils.isEmpty(locale)) {
                                    String[] split = null;
                                    if (locale.contains("_")) {
                                        split = locale.split("_");
                                    }
                                    String language = split != null && split.length > 0 ? split[0] : locale;
                                    String country = split != null && split.length > 1 ? split[1] : null;

                                    if (!LanguageManager.Language.ENGLISH.equalsIgnoreCase(language)) {
                                        String sLanguage = LanguageManager.get().getSystemLanguageOnNull(language, country);
                                        if (!LanguageManager.Language.ENGLISH.equalsIgnoreCase(sLanguage)) {
                                            return sLanguage;
                                        }
                                    }
                                } else {
                                    LocaleListCompat llc = ConfigurationCompat.getLocales(Resources.getSystem().getConfiguration());
                                    for (int i = 0; i < llc.size(); i++) {
                                        Locale local = llc.get(i);
                                        if (!EmptyUtils.isEmpty(local)) {
                                            String language = local.getLanguage();
                                            if (!EmptyUtils.isEmpty(language)) {
                                                if (!LanguageManager.Language.ENGLISH.equalsIgnoreCase(language)) {
                                                    String sLanguage = LanguageManager.get().getSystemLanguageOnNull(language, local.getCountry());
                                                    if (!LanguageManager.Language.ENGLISH.equalsIgnoreCase(sLanguage)) {
                                                        return sLanguage;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

            }
        }
        return LanguageManager.get().getSystemLanguageOnNull();
    }

    public static void getKeyboardInfo() {
        Activity context = LifecycleProvider.get().getTopActivity();
        if (context != null) {
            InputMethodManager imm = (InputMethodManager) context.getSystemService(Context.INPUT_METHOD_SERVICE);
            if (imm != null) {
                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
                    InputMethodSubtype lastInputMethodSubtype = imm.getLastInputMethodSubtype();
                    if (lastInputMethodSubtype != null) {
                        String languageTag = lastInputMethodSubtype.getLanguageTag();
                        String extraValue = lastInputMethodSubtype.getExtraValue();
                        String locale = lastInputMethodSubtype.getLocale();
                        String mode = lastInputMethodSubtype.getMode();
                        Logger.i("==> lastInputMethodSubtype", languageTag, extraValue, locale, mode);
                    }
                }

                InputMethodSubtype inputMethodSubtype = imm.getCurrentInputMethodSubtype();
                if (inputMethodSubtype != null) {
                    String locale = inputMethodSubtype.getLocale();
                    String extraValue = inputMethodSubtype.getExtraValue();
                    String mode = inputMethodSubtype.getMode();
                    Logger.i("==> inputMethodSubtype", locale, extraValue, mode);
                }

                String string = Settings.Secure.getString(context.getContentResolver(), Settings.Secure.DEFAULT_INPUT_METHOD);
                Logger.i("==> default_input_method", string);

                List<InputMethodInfo> list = getEnabledInputMethodList(imm);
                if (!EmptyUtils.isEmpty(list)) {
                    for (InputMethodInfo info : list) {
                        List<InputMethodSubtype> subtypes = imm.getEnabledInputMethodSubtypeList(info, true);
                        if (!EmptyUtils.isEmpty(subtypes)) {
                            StringBuilder builder = new StringBuilder();
                            for (InputMethodSubtype subtype : subtypes) {
                                if (subtype != null) {
                                    String locale = subtype.getLocale();
                                    String extraValue = subtype.getExtraValue();
                                    String mode = subtype.getMode();
                                    builder.append("locale = " + locale + ", extraValue = " + extraValue + ",  mode = " + mode + "\n");
                                }
                            }
                            Logger.i("==> getEnabledInputMethodList", builder.toString());
                        }
                    }
                }
            }
        }
    }

    @NonNull
    private static List<InputMethodInfo> getEnabledInputMethodList(@Nullable InputMethodManager imm) {
        List<InputMethodInfo> list = new ArrayList<>();
        try {
            if (imm != null) {
                list.addAll(imm.getEnabledInputMethodList());
            }
        } catch (Exception ex) {
            // no op
        }
        return list;
    }
}
