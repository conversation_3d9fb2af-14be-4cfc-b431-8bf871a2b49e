package com.sayweee.weee.service.analytics.factory.iml;

import android.content.Context;

import com.sayweee.weee.R;
import com.sayweee.weee.global.manager.OrderManager;
import com.sayweee.weee.module.account.bean.SimplePreOrderBean;
import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.module.cart.service.OrderHelper;
import com.sayweee.weee.service.analytics.factory.BaseEagleFactory;
import com.sayweee.weee.utils.EmptyUtils;
import com.sayweee.weee.widget.product.ProductView;
import com.sayweee.wrapper.helper.lifecycle.LifecycleProvider;

import java.io.Serializable;
import java.util.Map;


/**
 * Author:  winds
 * Date:    3/23/22.
 * Desc:
 */
public class ItemSmallEagleFactory extends BaseEagleFactory implements Serializable {

    @Override
    protected Map<String, Object> createContent(ProductBean bean, int position) {
        Map<String, Object> params = getDefaultProductContent();
        Context context = LifecycleProvider.get().getApplication().getApplicationContext();
        params.put("prod_pos", position);
        params.put("prod_id", bean.id);
        params.put("recommendation_trace_id", bean.recommendation_trace_id);
        if (!EmptyUtils.isEmpty(bean.discount_percentage)) {
            params.put("discount_percentage", bean.discount_percentage);
        }
        params.put("is_limit_product", bean.is_limit_product);
        params.put("sold_status", bean.sold_status);
        //name
        params.put("prod_name", bean.name);
        //pantry+商品,market place商品
        params.put("is_pantry", bean.is_pantry);
        params.put("is_mkpl", bean.isSeller());
        params.put("is_fbw", bean.isFbw());
        params.put("biz_type", bean.biz_type);
        params.put("is_sponsored", bean.is_sponsored);
        params.put("is_manual", bean.is_manual);

        //mark
        if (ProductView.isPreSell(bean.sold_status)) {
            // do nothing
        } else {
            if (bean.label_list != null && !bean.label_list.isEmpty()) {
                ProductBean.LabelListBean label = bean.label_list.get(0);
                params.put("label_list", "[" + label.label_name + "]");
            }
        }

        //price
        params.put("price", OrderHelper.formatConciseMoney(bean.price));
        if (bean.base_price > 0) {
            params.put("base_price", OrderHelper.formatConciseMoney(bean.base_price));
        }

        boolean restrictedParamsShowed = false;
        //优先级从上到下，依次降低
        //mkpl
        if (bean.isSeller()) {
            restrictedParamsShowed = true;
        }

        //BOGO
        boolean hasActivity = !EmptyUtils.isEmpty(bean.product_tag_list);
        if (hasActivity && !restrictedParamsShowed) {
            params.put("product_tag_list", bean.product_tag_list.get(0).tag_name);
            restrictedParamsShowed = true;
        }

        //remaining tip
        if (!restrictedParamsShowed) {
            boolean showRemainingTip = OrderHelper.isShowRemainingTip(bean.remaining_count, bean.sold_count);
            if (showRemainingTip) {
                params.put("product_tag_list", context.getResources().getString(R.string.s_remaining_tip, bean.remaining_count));
            }
        }

        int productId = bean.getProductId();
        String productKey = bean.getProductKey();
        SimplePreOrderBean.ItemsBean product = OrderManager.get().getSimpleOrderItem(productId, productKey);
        int num = product != null ? product.quantity : 0;
        if (num > 0) {
            params.put("qty_in_cart", num);
        }
        params.put("is_mkpl", bean.isSeller());
        if (bean.entrance_tag != null) {
            params.put("tag_key", bean.entrance_tag.tag_key);
            params.put("tag_name", bean.entrance_tag.tag_name);
        }
        params.put("volume_price_support", bean.volume_price_support);

        // Pre-sale
        params.put("is_presale", bean.is_presale);
        params.put("sale_event_id", bean.sale_event_id);
        putExtraProductContent(bean, params);
        return params;
    }

}
