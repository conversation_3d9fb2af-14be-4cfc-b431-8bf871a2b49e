package com.sayweee.weee.service.analytics.factory;

import com.sayweee.weee.module.cart.bean.CartItemBean;
import com.sayweee.weee.module.cart.bean.ProductBean;

import java.io.Serializable;
import java.util.Map;

/**
 * Author:  winds
 * Date:    3/23/22.
 * Desc:
 */
public interface IEagleFactory extends Serializable {

    IEagleFactory setTarget(ProductBean target, int position);

    IEagleFactory setCartTarget(CartItemBean target, int position);

    IEagleFactory setElement(Map<String, Object> element);

    IEagleFactory setContext(Map<String, Object> context);

    Map<String, Object> get();

}
