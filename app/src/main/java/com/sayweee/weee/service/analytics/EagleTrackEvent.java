package com.sayweee.weee.service.analytics;

/**
 * Desc:    app新埋点事件名
 */
public class EagleTrackEvent {

    public interface EventType {
        String BANNER_IMP = "t2_banner_imp";
        String ELLIPSE_IMP = "t2_ellipse_imp";
        String FILTER_BUTTON_IMP = "t2_filter_button_imp";
        String PROD_IMP = "t2_prod_imp";
        String CART_IMP = "t2_cart_imp";
        String PAGE_SEC_IMP = "t2_page_sec_imp";
        String CART_ACTION = "t2_cart_action";
        String CLICK_ACTION = "t2_click_action";
        String KEYBOARD_INPUT = "t2_keyboard_input";
        String POPUP_IMP = "t2_popup";
        String INFO_UPDATE = "t2_info_update";
        String STATUS_METRICS = "t2_status_metrics";
        // FIXME: we need to review this events and create it if needed
        String ADS_BANNER_IMP = "ads_banner_imp";
        String ADS_PROD_IMP = "ads_prod_imp";
        String ADS_CART_ACTION = "ads_cart_action";
    }

    public interface BannerType {
        String HOME_TOP_BANNER = "home_top_banner";
        String TOP_BANNER = "top_banner";
        String RTG_VENDOR = "rtg_vendor";
        String THEME = "theme";
        String HOME_VIDEO = "video";
        String BANNER_LINE = "banner_line";
        String MESSAGE = "message";
        String SOCIAL_EVENT = "social_event";
        String SOCIAL_TRENDING = "social_trending";
        String CATEGORY = "category";
        String CUISINE = "cuisine";
        String POST = "post";
        String POST_LIST = "post_list";
        String ACCOUNT = "account";
        String ACCOUNT_LIST = "account_list";
        String TAG = "tag";
        String TAG_LIST = "tag_list";
        String COLLECTION = "collection";
        String WEEKLY_FEATURED = "weekly_featured";
        String VENDOR = "vendor";
        String UPSELL_MESSAGE = "upsell_message";
        String ADS_BANNER = "ads_banner";
        String HOT_KEYWORD = "hot_keyword";
    }

    public interface BannerKey {
        String FREE_DELIVERY_INFO = "free_delivery_info";
    }

    public interface ClickType {
        String VIEW = "view";
        String NORMAL = "normal";

        String SAVE = "save";
        String UNSAVE = "unsave";
        String LIKE = "like";
        String UNLIKE = "unlike";
        String SHARE = "share";
        String FOLLOW = "follow";
        String UNFOLLOW = "unfollow";
        String COMMENT = "comment";
        String CLAIM = "claim";
        String PLAY = "play";
        String PAUSE = "pause";
    }

    public interface TargetType {
        String PRODUCT = "product";
        String SORT_FILTER = "sort_filter";
        String APPLY_BUTTON = "apply_button";
        String FILTER_BUTTON = "filter_button";
        String FILTER_SUB_BUTTON = "filter_sub_button";
        String SEARCH_TARGET = "search_target";
        String REVIEW = "review";
        String VIDEO = "video";
        String CATEGORY = "category";
        String CUISINE = "cuisine";
        String COLLECTION = "collection";
        String ELLIPSE_TYPE = "ellipse_type";
        String NORMAL_BUTTON = "normal_button";
        String SECTION = "section";
        String POST = "post";
        String PROFILE = "profile";
        String COMMENT = "comment";
        String BANNER = "banner";
        String SHARE_CHANNEL = "share_channel";
        String MKPL_SELLER = "mkpl_seller";
        String MESSAGE = "message";
        String REVIEW_TEXT = "review_text";
        String POPUP = "popup";
        String BANNER_LINE = "banner_line";
        String COUPON = "coupon";
        String VENDOR = "vendor";
        String FBW_SELLER = "fbw_seller";
        String PROMOTION_GIFT = "promotion_gift";
        String ADS_BANNER = "ads_banner";
        String ADS_AVATAR = "ads_avatar";
        String ADS_PRODUCT_ITEM = "ads_product_item";
        String ADS_PRODUCT_PDP = "ads_product_pdp";
        String UPSELL_MESSAGE = "upsell_message";
        String SECTION_VIEW_ALL = "section_view_all";
        String PRODUCT_NOTIFY_ME = "product_notify_me";
        String CHART = "chart";
    }

    public interface Key {
        //String PROD_POS = "prod_pos";
    }

    public interface Ctx {
        String PAGE_TARGET = "page_target";
    }

    public interface TargetNm {
        String TARGET_VIEW_ALL = "view_all";
        String TARGET_POST = "post";
        String EXPLORE_MORE = "explore_more";
        String SHOP_MORE = "shop_more";
        String CLAIM = "claim";
        String CLAIM_ALL = "claim_all";
        String USE = "use";
        String USE_NOW = "use_now";
        String DELIVERY_DATE = "delivery_date";
        String GROUP_ORDER = "group_order";
        String SELLER_GROUP_ORDER = "seller_group_order";
        String CHECKOUT = "checkout";
        String FOLLOW = "follow";
        String COUPON = "coupon";
        String VIEW_MORE = "view_more";
        String FREE_DELIVERY_INFO = "free_delivery_info";
        String CLOSE = "close";
        String SHIPPING_FEE = "shipping_fee";
        String ALL_PRESALE = "all_presale";
        String SERVICE_FEE = "service_fee";
        String CHECK_BALANCE = "check_balance";
        String EDIT_AMOUNT = "edit_amount";
        String BACK = "back";
        String ENTER = "enter";
        String CONFIRM = "confirm";
        String SAVE = "save";
        String WEEE_POINTS = "weee_points";
        String UPDATE_ADDR = "update_addr";
        String CART = "cart";
        String CONFIRM_PAYMENT_METHOD = "confirm_payment_method";
    }

    public interface ModNm {
        String ITEM_LIST = "item_list";
        String VIDEO_DETAIL = "video_detail";
        String COMM_PROFILE = "comm_profile";
        String FILTER_BUTTONS = "filter_buttons";
        String FILTER_SUB_BUTTONS = "filter_sub_buttons";
        String CM_THEME = "cm_theme";
        String CM_BANNER_LINE = "cm_banner_line";
        String PRODUCT_DETAIL = "product_detail";
        String BAG_POPUP_ITEM_LIST = "bag_popup_item_list";
        String SYSTEM_POPUP = "system_popup";
        String FILTER_TABS = "filter_tabs";
        String PRODUCT_REVIEW = "product_review";
        String VIDEO_REVIEW = "video_review";
        String VIDEO_LIST = "video_list";
        String CART = "cart";
        String DELIVERY_INFO = "delivery_info";
        String PAYMENT_METHOD = "payment_method";
        String PAYMENT_METHODS = "payment_methods";
        String APPLY_COUPON = "apply_coupon";
        String PURCHASE_SUMMARY = "purchase_summary";
        String MKPL_CART_RECOMMEND = "mkpl_cart_recommend";
        String MKPL_CART_ITEM = "mkpl_cart_item";
        String CM_CONTENT_FEED = "cm_content_feed";
        String SELLER_TOP_MESSAGE = "seller_top_message";
        String ORDER_SUMMARY = "order_summary";
        String REWARDS_ANNCMNT = "rewards_anncmnt";
        String UPSELL_POPUP = "upsell_popup";
        String COUPON_POPUP = "coupon_popup";
        String PRESALE_POPUP = "presale_popup";
        String PRESALE_MESSAGE = "presale_message";
        String HORIZONTAL_ITEM_LIST = "horizontal_item_list";
        String POPOVER_PRODUCTS_ITEM_LIST = HORIZONTAL_ITEM_LIST; // "popover_products_item_list";
        String PROMOTION_BAR = "promotion_bar";
        String GENERAL_POPUP = "general_popup";
        String BENEFITS = "benefits";
    }

    public interface SecNm {
        String NORMAL = "normal";
    }

    public interface InfoName {
        String PAYMENT_METHOD = "payment_method";
        String PAYMENT_METHODS = "payment_methods";
        String BENEFITS = "benefits";
        //String SHIPPING_ADDRESS = "shipping_address";
        String SHIPPING_ADDRESS = "delivery_address";
        String DELIVERY_DATE = "delivery_date";
        String APPLY_COUPON = "apply_coupon";
    }

    public interface ActionType {
        String ADD = "add";
        String UPDATE = "update";
        String CHANGE = "change";
        String DELETE = "delete";
        String SELECT = "select";
        String SKIP = "skip";
        String UNSELECT = "unselect";
        String APPLY = "apply";
        String CANCEL = "cancel";
        String REDEEM = "redeem";
    }

    public interface PageTab {
        String REVIEWS = "reviews";
    }

    public interface PopupNm {
        String CHECK_BALANCE_POPUP = "check_balance_popup";
        String EDIT_AMOUNT_POPUP = "edit_amount_popup";
        String PIN_ENTER_POPUP = "pin_enter_popup";
        String WEEE_POINTS_POPUP = "weee_points_popup";
    }

    public interface PopupAction {
        String VIEW = "view";
        String CLICK = "click";
    }

}
