package com.sayweee.weee.service.timer.bean;

import com.sayweee.weee.service.timer.TimerBannerManager;
import com.sayweee.weee.utils.EmptyUtils;

import java.io.Serializable;
import java.util.List;

/**
 * Author:  winds
 * Date:    2023/3/22.
 * Desc:
 */
public class TimerBannerBean {
    /**
     * pos : top
     * priority : 1
     * color : #ccc
     * bg_color : #ccc
     * bg_img :
     * link :
     * icon_url :
     * show_time : 10
     * title : {"text":"文本 text","sub_text":"文本 sub_text"}
     * countdown : {"title":"countdown 文本 title","type":2,"end_time":1112323,"number_color":"#22ffaa","number_bg_color":"#22ffaa"}
     * right_cta : {"title":"View More","type":1,"link":""}
     * close_cta : {"visible":true,"icon_url":"","link":""}
     * pages : ["page_home","page_category","page_cart","page_me","page_search","page_product_details","page_bogo_collect"]
     */

    public String id;
    public String pos;
    public int priority;
    public String color;
    public String bg_color;
    public String bg_img;
    public String link;
    public String icon_url;
    public Title title;
    public Countdown countdown;
    public RightCta right_cta;
    public CloseCta close_cta;
    public List<String> pages;
    public String style;
    public String display_style;
    public int percent;

    public static class Title implements Serializable {
        public String text;
        public String sub_text;
    }

    public static class Countdown implements Serializable {
        public String title;
        public int type; //1 方块状  2 小数字  3 倒计时自动消失
        public long end_time;
        public long server_timestamp;
        public String number_color;
        public String number_bg_color;
        public int show_time;
    }

    public static class RightCta implements Serializable {
        public String title;
        public String type;
        public String link;
        public String title_bg_color;
    }

    public static class CloseCta implements Serializable {
        public boolean visible;
        public String icon_url;
        public String link;
    }

    public boolean isIconViewVisible() {
        return !EmptyUtils.isEmpty(icon_url);
    }

    public boolean isCountdownViewVisible() {
        return countdown != null && getCountdownValue() > 0;
    }

    public int getCountdownType() {
        return countdown != null ? countdown.type : TimerBannerManager.TYPE_IDLE;
    }

    public long getCountdownValue() {
        int countdownType = getCountdownType();
        if (countdownType == 1 || countdownType == 2) {
            return countdown.end_time - countdown.server_timestamp;
        } else if (countdownType == 3) {
            return countdown.show_time;
        }
        return 0;
    }

    public boolean isIdleTimer() {
        return getCountdownType() == TimerBannerManager.TYPE_IDLE;
    }

    public boolean isRightButtonVisible() {
        return right_cta != null && !EmptyUtils.isEmpty(right_cta.title);
    }

    public boolean isCloseButtonVisible() {
        return close_cta != null && close_cta.visible && !EmptyUtils.isEmpty(close_cta.icon_url);
    }

    public boolean isDisplayProgress(){
        return "delivery".equals(display_style) || isDisplayStyleDone();
    }

    public boolean isDisplayStyleDone(){
        return "done".equals(display_style);
    }
}
