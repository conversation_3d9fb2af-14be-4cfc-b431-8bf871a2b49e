package com.sayweee.weee.service.location;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

public class LocationMap {

    static HashMap<String, String> map;
    static HashMap<String, String> fullMap;

    private static void initAll() {
        init();
        initFull();
    }

    private static void init() {
        if (map == null) {
            map = new HashMap<>();
            map.put("37", "AK");
            map.put("38", "AL");
            map.put("39", "AR");
            map.put("40", "AZ");
            map.put("41", "CA");
            map.put("42", "CO");
            map.put("43", "CT");
            map.put("44", "DC");
            map.put("45", "DE");
            map.put("46", "FL");
            map.put("47", "GA");
            map.put("48", "HI");
            map.put("49", "IA");
            map.put("50", "ID");
            map.put("51", "IL");
            map.put("52", "IN");
            map.put("53", "KS");
            map.put("54", "KY");
            map.put("55", "LA");
            map.put("56", "MA");
            map.put("57", "MD");
            map.put("58", "ME");
            map.put("59", "MI");
            map.put("60", "MN");
            map.put("61", "MO");
            map.put("62", "MS");
            map.put("63", "MT");
            map.put("64", "NC");
            map.put("65", "ND");
            map.put("66", "NE");
            map.put("67", "NH");
            map.put("68", "NJ");
            map.put("69", "NM");
            map.put("70", "NV");
            map.put("71", "NY");
            map.put("72", "OH");
            map.put("73", "OK");
            map.put("74", "OR");
            map.put("75", "PA");
            map.put("76", "RI");
            map.put("77", "SC");
            map.put("78", "SD");
            map.put("79", "TN");
            map.put("80", "TX");
            map.put("81", "UT");
            map.put("82", "VA");
            map.put("83", "VT");
            map.put("84", "WA");
            map.put("85", "WI");
            map.put("86", "WV");
            map.put("87", "WY");
        }
    }


    private static void initFull() {
        if (fullMap == null) {
            fullMap = new LinkedHashMap<>();
            fullMap.put("37", "Alaska");
            fullMap.put("38", "Alabama");
            fullMap.put("39", "Arkansas");
            fullMap.put("40", "Arizona");
            fullMap.put("41", "California");
            fullMap.put("42", "Colorado");
            fullMap.put("43", "Connecticut");
            fullMap.put("44", "District of Columbia");
            fullMap.put("45", "Delaware");
            fullMap.put("46", "Florida");
            fullMap.put("47", "Georgia");
            fullMap.put("48", "Hawaii");
            fullMap.put("49", "Iowa");
            fullMap.put("50", "Idaho");
            fullMap.put("51", "Illinois");
            fullMap.put("52", "Indiana");
            fullMap.put("53", "Kansas");
            fullMap.put("54", "Kentucky");
            fullMap.put("55", "Louisiana");
            fullMap.put("56", "Massachusetts");
            fullMap.put("57", "Maryland");
            fullMap.put("58", "Maine");
            fullMap.put("59", "Michigan");
            fullMap.put("60", "Minnesota");
            fullMap.put("61", "Missouri");
            fullMap.put("62", "Mississippi");
            fullMap.put("63", "Montana");
            fullMap.put("64", "North Carolina");
            fullMap.put("65", "North Dakota");
            fullMap.put("66", "Nebraska");
            fullMap.put("67", "New Hampshire");
            fullMap.put("68", "New Jersey");
            fullMap.put("69", "New Mexico");
            fullMap.put("70", "Nevada");
            fullMap.put("71", "New York");
            fullMap.put("72", "Ohio");
            fullMap.put("73", "Oklahoma");
            fullMap.put("74", "Oregon");
            fullMap.put("75", "Pennsylvania");
            fullMap.put("76", "Rhode Island");
            fullMap.put("77", "South Carolina");
            fullMap.put("78", "South Dakota");
            fullMap.put("79", "Tennessee");
            fullMap.put("80", "Texas");
            fullMap.put("81", "Utah");
            fullMap.put("82", "Virginia");
            fullMap.put("83", "Vermont");
            fullMap.put("84", "Washington");
            fullMap.put("85", "Wisconsin");
            fullMap.put("86", "West Virginia");
            fullMap.put("87", "Wyoming");
        }
    }

    public static String getState(String number) {
        if (number == null) {
            return null;
        }
        initAll();
        return map.get(number);
    }

    public static String getFullState(String number) {
        if (number == null) {
            return null;
        }
        initAll();
        return fullMap.get(number);
    }

    public static List<String> getFullStateList() {
        initAll();
        ArrayList<String> list = new ArrayList<>();
        if (fullMap != null) {
            for (Map.Entry<String, String> m : fullMap.entrySet()) {
                list.add(m.getValue());
            }
        }
        return list;
    }

    public static String getStateId(String fullState) {
        initAll();
        for (String key : fullMap.keySet()) {
            String value = fullMap.get(key);
            if (fullState != null && fullState.equals(value)) {
                return key;
            }
        }
        return getShortStateId(fullState);
    }

    public static String getShortStateId(String shortState) {
        initAll();
        for (String key : map.keySet()) {
            String value = map.get(key);
            if (shortState != null && shortState.equals(value)) {
                return key;
            }
        }
        return shortState;
    }
}
