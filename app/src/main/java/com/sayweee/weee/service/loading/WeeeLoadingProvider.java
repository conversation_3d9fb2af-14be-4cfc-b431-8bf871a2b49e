package com.sayweee.weee.service.loading;

import android.app.Activity;

import com.sayweee.weee.R;
import com.sayweee.weee.module.dialog.LoadingAnimDialog;
import com.sayweee.wrapper.base.view.WrapperDialog;
import com.sayweee.wrapper.helper.loading.ILoadingProvider;

/**
 * Author:  winds
 * Email:   <EMAIL>
 * Date:    2020/11/6.
 * Desc:
 */
public class WeeeLoadingProvider implements ILoadingProvider {
    @Override
    public WrapperDialog create(Activity activity) {
        return new LoadingAnimDialog(activity, R.style.CommonDialogTheme);
    }
}
