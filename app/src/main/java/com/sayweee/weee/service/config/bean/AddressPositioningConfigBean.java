package com.sayweee.weee.service.config.bean;

import java.io.Serializable;

public class AddressPositioningConfigBean implements Serializable {
    public boolean foreground_positioning_disable;
    public boolean home_positioning_enable;
    public int home_positioning_check_distance;
    public boolean address_positioning_enable;
    public int address_positioning_check_distance;

    public boolean isHomePositioningEnable() {
        return home_positioning_enable && home_positioning_check_distance > 0;
    }

    public boolean isAddressPositioningEnable() {
        return address_positioning_enable && address_positioning_check_distance > 0;
    }
}
