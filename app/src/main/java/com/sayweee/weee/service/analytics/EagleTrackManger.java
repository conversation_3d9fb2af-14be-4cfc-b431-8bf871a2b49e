package com.sayweee.weee.service.analytics;

import androidx.annotation.Nullable;
import androidx.collection.ArrayMap;

import com.sayweee.weee.service.analytics.bean.EagleContext;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Desc:管理页面eagle track
 */
public class EagleTrackManger {

    private Map<String, List<String>> trackHistoryData = new HashMap<>();

    public static final String PAGE_HOME = "HOME";
    public static final String PAGE_CATEGORY = "CATEGORY";
    public static final String PAGE_SEARCH = "SEARCH";
    public static final String PAGE_ME = "ME";
    public static final String PAGE_POST_SEARCH = "POST_SEARCH";
    public static final String PAGE_THEME = "THEME";
    public static final String PAGE_THEME_PAGER = "THEME_PAGER";
    public static final String PAGE_IMAGE_SEARCH = "IMAGE_SEARCH";
    public static final String PAGE_MKPL = "MKPL";
    public static final String PAGE_GLOBAL = "global";
    public static final String PAGE_SELLER_PRODUCT = "seller_product";
    public static final String PAGE_BRAND = "brand";
    public static final String PAGE_Ai = "AI_BOT";
    public static final String PAGE_COLLECTION = "COLLECTION";

    public static EagleTrackManger get() {
        return Builder.instance;
    }

    private static final class Builder {
        private static final EagleTrackManger instance = new EagleTrackManger();
    }

    public boolean isEventTracked(String page, String key) {
        List<String> list = trackHistoryData.get(page);
        return list != null && list.contains(key);
    }

    public void setEventTracked(String page, String key) {
        List<String> list = trackHistoryData.get(page);
        if (list == null) {
            list = new ArrayList<>();
        }
        list.add(key);
        trackHistoryData.put(page, list);
    }

    public void resetHomeTrackData() {
        resetTrackData(PAGE_HOME);
    }

    public void resetTrackData(String page) {
        trackHistoryData.remove(page);
    }

    public void resetTrackData() {
        trackHistoryData.clear();
    }

    public Map<String, Object> getElement(String modNm, int modPos, String secNm, int secPos) {
        Map<String, Object> map = new ArrayMap<>();
        map.put("mod_nm", modNm);
        map.put("mod_pos", modPos == -1 ? null : modPos);
        map.put("sec_nm", secNm);
        map.put("sec_pos", secPos == -1 ? null : secPos);
        return map;
    }

    public Map<String, Object> getCtx(String filter_sub_category, String catalogue_num, String sort, Map<String, String> filters, String page_target, String page_tab) {
        return getCtx(filter_sub_category, catalogue_num, sort, filters, page_target, page_tab, null);
    }

    public Map<String, Object> getCtx(String filter_sub_category, String catalogue_num, String sort, Map<String, String> filters, String page_target, String page_tab, String brand_name) {
        return getCtx(filter_sub_category, catalogue_num, sort, filters, page_target, page_tab, brand_name, null);
    }

    public Map<String, Object> getCtx(String filter_sub_category, String catalogue_num, String sort, Map<String, String> filters, String page_target, String page_tab, String brand_name, String global_vendor) {
        return getCtx(filter_sub_category, catalogue_num, sort, filters, page_target, page_tab, brand_name, null, null);
    }

    public Map<String, Object> getCtx(String filter_sub_category, String catalogue_num, String sort, Map<String, String> filters, String page_target, String page_tab, String brand_name, String global_vendor, String trace_id) {
        Map<String, Object> map = new ArrayMap<>();
        map.put("filter_sub_category", filter_sub_category);
        map.put("catalogue_num", catalogue_num);
        map.put("sort", sort);
        map.put("filters", filters);
        map.put("page_target", page_target);
        map.put("page_tab", page_tab);
        map.put("brand_name", brand_name);
        map.put("global_vendor", global_vendor);
        map.put("trace_id", trace_id);
        return map;
    }

    @Nullable
    public Map<String, Object> getCtx(@Nullable EagleContext ctx) {
        return ctx != null ? ctx.asMap() : null;
    }

    public void trackEagleClickAction(String modNm, String targetNm, int targetPos, String targetType, String clickType) {
        trackEagleClickAction(modNm, -1, null, -1, targetNm, targetPos, targetType, clickType);
    }


    public void trackEagleClickAction(String targetNm, int targetPos, String targetType, String clickType) {
        trackEagleClickAction(targetNm, targetPos, targetType, clickType, null);
    }

    public void trackEagleClickAction(String targetNm, int targetPos, String targetType, String clickType, Map<String, Object> ctx) {
        trackEagleClickAction(null, -1, null, -1, targetNm, targetPos, targetType, clickType, ctx);
    }

    public void trackEagleClickAction(String targetNm, int targetPos, String targetType, String clickType, Map<String, Object> ctx, Boolean isMkpl) {
        trackEagleClickAction(null, -1, null, -1, targetNm, targetPos, targetType, clickType, ctx, null, isMkpl);
    }

    public void trackEagleClickAction(String modNm, int modPos, String secNm, int secPos, String targetNm, int targetPos, String targetType, String clickType) {
        trackEagleClickAction(modNm, modPos, secNm, secPos, targetNm, targetPos, targetType, clickType, null, null, null);
    }

    /**
     * 点击事件携带is_select参数
     */
    public void trackEagleClickAction(String modNm, int modPos, String secNm, int secPos, String targetNm, int targetPos, String targetType, String clickType, Boolean isSelect) {
        trackEagleClickAction(modNm, modPos, secNm, secPos, targetNm, targetPos, targetType, clickType, null, isSelect, null);
    }

    /**
     * 点击事件携带ctx参数
     */
    public void trackEagleClickAction(String modNm, int modPos, String secNm, int secPos, String targetNm, int targetPos, String targetType, String clickType, Map<String, Object> ctx) {
        trackEagleClickAction(modNm, modPos, secNm, secPos, targetNm, targetPos, targetType, clickType, ctx, null, null);
    }

    public void trackEagleClickAction(String modNm, int modPos, String secNm, int secPos, String targetNm, int targetPos, String targetType, String clickType, Map<String, Object> ctx, Boolean isSelect, Boolean isMkpl) {
        AppAnalytics.logClickAction(new EagleTrackModel.Builder()
                .setMod_nm(modNm)
                .setMod_pos(modPos)
                .setSec_nm(secNm)
                .setSec_pos(secPos)
                .setTargetNm(targetNm)
                .setTargetPos(targetPos)
                .setTargetType(targetType)
                .setClickType(clickType)
                .setIsSelect(isSelect)
                .setIsMkpl(isMkpl)
                .addCtx(ctx)
                .build().getParams());
    }

    public void trackEagleInfoUpdateAddress(String info_name, String action_type, Boolean result, String address_id, Map<String, Object> map) {
        trackEagleInfoUpdate(info_name, action_type, result, null, null, null, address_id, map, null);
    }

    public void trackEagleInfoUpdate(String info_name, String action_type, Boolean result, String payment_type, Map<String, Object> map) {
        trackEagleInfoUpdate(info_name, action_type, result, payment_type, null, map);
    }

    public void trackEagleInfoUpdate(String info_name, String action_type, Boolean result, String payment_type, String delivery_date, Map<String, Object> map) {
        trackEagleInfoUpdate(info_name, action_type, result, payment_type, delivery_date, null, null, map, null);
    }

    public void trackEagleInfoUpdate(String info_name, String action_type, Boolean result, String store) {
        trackEagleInfoUpdate(info_name, action_type, result, null, null, null, null, null, store);
    }

    public void trackEagleInfoUpdate(String info_name, String action_type, Boolean result, String payment_type, String delivery_date, String coupon_code, String address_id, Map<String, Object> map, String store) {
        AppAnalytics.logInfoUpdate(new EagleTrackModel.Builder()
                .setMod_nm(null)
                .setMod_pos(-1)
                .setSec_nm(null)
                .setSec_pos(-1)
                .setInfo_name(info_name)
                .setAction_type(action_type)
                .setInfoResult(result != null ? result : false)
                .setPayment_type(payment_type)
                .setDelivery_date(delivery_date)
                .setCouponCode(coupon_code)
                .setAddressId(address_id)
                .setStore(store)
                .addCtx("delivery_address".equalsIgnoreCase(info_name) ? map : null)
                .build().getParams());
    }
}
