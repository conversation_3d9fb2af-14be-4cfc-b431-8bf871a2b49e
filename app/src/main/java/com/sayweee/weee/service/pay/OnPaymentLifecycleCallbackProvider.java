package com.sayweee.weee.service.pay;

import androidx.annotation.NonNull;

import com.sayweee.weee.module.checkout.bean.PayBean;

public interface OnPaymentLifecycleCallbackProvider {

    @NonNull
    OnPaymentLifecycleCallback getOnPaymentLifecycleCallback(boolean showLoading);

    interface OnPaymentLifecycleCallback extends PaymentHelper.OnPaymentEventCallback {
        void onBegin(PayBean payBean);
    }
}
