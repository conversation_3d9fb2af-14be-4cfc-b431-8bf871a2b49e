package com.sayweee.weee.service.analytics;

import com.sayweee.weee.service.track.TrackParams;
import com.sayweee.weee.utils.EmptyUtils;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * Desc:    新埋点通用配置参数
 */
public class EagleTrackModel {
    private Map<String, Object> element;
    private Map<String, Object> content;
    private Map<String, Object> context;

    private EagleTrackModel(Builder builder) {
        this.element = builder.element;
        this.content = new TrackParams().put("co", builder.content).get();
        if (!EmptyUtils.isEmpty(builder.context)) {
            this.context = new TrackParams().put("ctx", builder.context).get();
        }
    }

    public Map<String, Object> getParams() {
        return new TrackParams().putAll(element).putAll(content).putAll(context).get();
    }

    /**
     * 单独获取Element模块信息
     */
    public Map<String, Object> getElement() {
        return element;
    }

    public static class Builder {
        private Map<String, Object> element;//位置信息
        private Map<String, Object> content;//内容信息
        private Map<String, Object> context;//上下文信息

        public Builder() {
            initElement();
            initContent();
            initContext();
        }

        /**
         * The WHERE (Page ⇒ Module ⇒ Section ⇒ Element)
         */
        private void initElement() {
            element = new HashMap<>();
            setMod_nm(null);
            setMod_pos(-1);
            setSec_nm(null);
            setSec_pos(-1);
        }

        public Builder addElement(Map<String, Object> element) {
            if (!EmptyUtils.isEmpty(element)) {
                this.element.putAll(element);
            }
            return this;
        }

        /**
         * The WHAT (a banner,  a product,  a message, an ellipse icon etc)
         */
        private void initContent() {
            content = new HashMap<>();
        }

        public Builder addContent(Map<String, Object> content) {
            if (!EmptyUtils.isEmpty(content)) {
                this.content.putAll(content);
            }
            return this;
        }

        /**
         * The CONTEXT
         */
        private void initContext() {
            context = new HashMap<>();
        }

        public Builder addCtx(Map<String, Object> context) {
            if (!EmptyUtils.isEmpty(context)) {
                this.context.putAll(context);
            }
            return this;
        }

        /******************************************************************************************/
        public Builder setMod_nm(String mod_nm) {
            putValue(element, "mod_nm", mod_nm);
            return this;
        }

        public Builder setMod_pos(int mod_pos) {
            if (mod_pos != -1) {
                putValue(element, "mod_pos", mod_pos);
            } else {
                putValue(element, "mod_pos", null);
            }
            return this;
        }

        public Builder setSec_nm(String sec_nm) {
            putValue(element, "sec_nm", sec_nm);
            return this;
        }

        public Builder setSec_pos(int sec_pos) {
            if (sec_pos != -1) {
                putValue(element, "sec_pos", sec_pos);
            } else {
                putValue(element, "sec_pos", null);
            }
            return this;
        }

        /****************************************banner********************************************/
        public Builder setBanner_id(int banner_id) {
            if (banner_id != -1) {
                setBannerId(String.valueOf(banner_id));
            }
            return this;
        }

        public Builder setBannerId(String bannerId) {
            if ("-1".equalsIgnoreCase(bannerId)) {
                putNonValue(content, "banner_id", null);
            } else {
                putNonValue(content, "banner_id", bannerId);
            }
            return this;
        }

        public Builder setBanner_key(String banner_key) {
            putNonValue(content, "banner_key", banner_key);
            return this;
        }

        public Builder setBanner_pos(int banner_pos) {
            putNonValue(content, "banner_pos", banner_pos);
            return this;
        }

        public Builder setStatus(String status) {
            putNonValue(content, "status", status);
            return this;
        }

        public Builder setMessage_type(String message_type) {
            putNonValue(content, "message_type", message_type);
            return this;
        }

        public Builder setBanner_type(String banner_type) {
            putNonValue(content, "banner_type", banner_type);
            return this;
        }

        public Builder setUrl(String url) {
            putNonValue(content, "url", url);
            return this;
        }

        public Builder setTargetNm(String target_nm) {
            putNonValue(content, "target_nm", target_nm);
            return this;
        }

        public Builder setTargetPos(int target_pos) {
            putNonValue(content, "target_pos", target_pos);
            return this;
        }

        public Builder setTargetType(String target_type) {
            putNonValue(content, "target_type", target_type);
            return this;
        }

        public Builder setClickType(String click_type) {
            putNonValue(content, "click_type", click_type);
            return this;
        }

        /**
         * 点击事件里面的is_select状态
         */
        public Builder setIsSelect(Boolean is_select) {
            putNonValue(content, "is_select", is_select);
            return this;
        }

        /**
         * 点击事件里面的click result状态
         */
        public Builder setResult(Boolean result) {
            putNonValue(content, "result", result);
            return this;
        }

        public Builder setClickResult(Boolean result) {
            putNonValue(content, "click_result", result);
            return this;
        }

        public Builder setIsMkpl(Boolean is_mkpl) {
            putNonValue(content, "is_mkpl", is_mkpl);
            return this;
        }

        /****************************************ellipse*******************************************/
        public Builder setEllipse_label(String ellipse_label) {
            putNonValue(content, "ellipse_label", ellipse_label);
            return this;
        }

        public Builder setEllipse_pos(int ellipse_pos) {
            putNonValue(content, "ellipse_pos", ellipse_pos);
            return this;
        }

        public Builder setEllipse_type(String ellipse_type) {
            putNonValue(content, "ellipse_type", ellipse_type);
            return this;
        }

        public Builder setIs_new(boolean is_new) {
            putNonValue(content, "is_new", is_new);
            return this;
        }

        public Builder setButton_nm(String button_nm) {
            putNonValue(content, "button_nm", button_nm);
            return this;
        }

        public Builder setButton_pos(int button_pos) {
            putNonValue(content, "button_pos", button_pos);
            return this;
        }

        public Builder setIs_selected(boolean is_selected) {
            putNonValue(content, "is_selected", is_selected);
            return this;
        }

        public Builder setInfo_name(String info_name) {
            putNonValue(content, "info_name", info_name);
            return this;
        }

        public Builder setStore(String store) {
            putNonValue(content, "store", store);
            return this;
        }

        public Builder setAction_type(String action_type) {
            putNonValue(content, "action_type", action_type);
            return this;
        }

        public Builder setInfoResult(boolean result) {
            putNonValue(content, "result", result ? "success" : "fail");
            return this;
        }

        public Builder setPayment_type(String payment_type) {
            putNonValue(content, "payment_type", payment_type);
            return this;
        }

        public Builder setDelivery_date(String delivery_date) {
            putNonValue(content, "delivery_date", delivery_date);
            return this;
        }

        public Builder setCouponCode(String coupon_code) {
            putNonValue(content, "coupon_code", coupon_code);
            return this;
        }

        public Builder setAddressId(String address_id) {
            putNonValue(content, "address_id", address_id);
            return this;
        }

        public EagleTrackModel build() {
            return new EagleTrackModel(this);
        }

        public void putNonValue(Map<String, Object> params, String key, Object value) {
            if (params != null && value != null && key != null) {
                if (value instanceof Integer && ((Integer) value) == -1) {
                    return;
                }
                params.put(key, value);
            }
        }

        public void putValue(Map<String, Object> params, String key, Object value) {
            if (params != null && key != null) {
                params.put(key, value);
            }
        }
    }

    public void filterNullValue(Map<String, Object> params) {
        if (params != null && params.size() > 0) {
            Iterator<Map.Entry<String, Object>> iterator = params.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry<String, Object> entry = iterator.next();
                if (entry.getValue() == null) {
                    iterator.remove();
                }
            }
        }
    }
}
