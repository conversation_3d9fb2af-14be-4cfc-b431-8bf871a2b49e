package com.sayweee.weee.service.search;

import com.sayweee.weee.module.cart.bean.FilterProductListBean;
import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.module.search.bean.SearchBean;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Author:  Chuan
 * Email:   <EMAIL>
 * Date:    2022/6/8
 * UpdateDate:
 * Desc:    activity 与 fragment共用的参数
 */
public class SearchManager {
    // 当前页面所有分类的缓存
    public final Map<String, List<ProductBean>> dateCache = new HashMap<>();
    // 当前页面的分类的商品缓存数量
    public final Map<String, Integer> filterTotalCountCache = new HashMap<>();
    public Map<String, Integer> totalCountCache = new HashMap<>();
    public String filter;
    public String sorts;
    private String keyWord;
    // 当前页面的分类的filter缓存
    public Map<String, SearchBean> filterCache = new HashMap<>();

    public static SearchManager get() {
        return Builder.instance;
    }

    private static final class Builder {
        private static final SearchManager instance = new SearchManager();
    }

    public void setFilterAndSorts(String filter, String sort){
        this.filter = filter;
        this.sorts = sort;
    }

    public void setKeyWord(String keyWord){
        release();
        this.keyWord = keyWord;
    }

    public void setFilter(String filter){
        this.filter = filter;
    }

    public void setSorts(String sorts){
        this.sorts = sorts;
    }

    public String getKeyWord(){
        return keyWord;
    }

    public String getSorts(){
        return sorts;
    }

    /**
     *  保存对应的分类商品列表和大小缓存
     */
    public void saveCache(String catalogueNum, FilterProductListBean filterProductListBean) {
        List<ProductBean> list = getCache(catalogueNum);
        List<ProductBean> empty = new ArrayList<>();
        if (list != null) {
            if (filterProductListBean != null) {
                list.addAll(filterProductListBean.products);
                dateCache.put(catalogueNum, list);
            }
        } else {
            dateCache.put(catalogueNum, filterProductListBean == null ? empty : filterProductListBean.products);
        }
        filterTotalCountCache.put(catalogueNum, filterProductListBean == null ? 0 : filterProductListBean.filter_total_count);
    }

    public boolean isLoadMoreEnd(String catalogueNum){
        List<ProductBean> curCache = getCache(catalogueNum);
        if (curCache == null || curCache.size() >= getCategoryNum(catalogueNum)) {
            return true;
        }
        return false;
    }

    public int getCategoryNum(String catalogueNum){
        int filterCountCache = 0;
        Integer integer = filterTotalCountCache.get(catalogueNum);
        if(integer != null){
            filterCountCache = integer;
        }
        return filterCountCache;
    }

    public List<ProductBean> getCache(String catalogueNum) {
        return dateCache.get(catalogueNum);
    }

    public void clearCaches() {
        dateCache.clear();
        filterTotalCountCache.clear();
        totalCountCache.clear();
    }

    public void release(){
        keyWord = null;
        filter = null;
        sorts = null;
        dateCache.clear();
        filterTotalCountCache.clear();
        totalCountCache.clear();
    }

}
