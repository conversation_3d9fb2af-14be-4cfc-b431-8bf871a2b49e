package com.sayweee.weee.react.component;

import android.content.Context;
import android.graphics.Typeface;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.res.ResourcesCompat;

import com.facebook.react.bridge.ReadableArray;
import com.facebook.react.common.assets.ReactFontManager;
import com.facebook.react.uimanager.annotations.ReactProp;
import com.facebook.react.views.text.ReactTextView;
import com.facebook.react.views.text.ReactTextViewManager;

import java.util.HashMap;
import java.util.Map;

@com.facebook.react.module.annotations.ReactModule(name = "RCTText")
public class RNWeeeTextViewManager extends ReactTextViewManager {

    public static final String REACT_CLASS = "RCTText";

    @Override
    public String getName() {
        return REACT_CLASS;
    }

    @ReactProp(name = "fontFamily")
    public void setFontFamily(ReactTextView view, @Nullable String fontFamily) {
        if (fontFamily != null) {
            view.setTypeface(ResFontManager.getTypeface(fontFamily, view.getContext()));
        }
    }

    private class ResFontManager {

        private static final Map<String, Typeface> sFontCache = new HashMap<>();

        // fontFamily 对应于 res/font/ 目录下的文件名（不含扩展名）
        public static Typeface getTypeface(String fontFamily, Context context) {
            if (sFontCache.containsKey(fontFamily)) {
                return sFontCache.get(fontFamily);
            }

            try {
                // 这是关键：通过 getIdentifier 动态获取资源ID
                // "font" 是资源类型，context.getPackageName() 是你的应用包名
                int resourceId = context.getResources().getIdentifier(
                        fontFamily,
                        "font",
                        context.getPackageName()
                );

                if (resourceId != 0) {
                    Typeface typeface = ResourcesCompat.getFont(context, resourceId);
                    sFontCache.put(fontFamily, typeface);
                    return typeface;
                }
            } catch (Exception e) {
                // 字体未找到或加载失败，可以打印日志
                e.printStackTrace();
            }

            // 如果在 res/font 中找不到，回退到 RN 的默认加载逻辑
            return ReactFontManager.getInstance().getTypeface(fontFamily, Typeface.NORMAL, context.getAssets());
        }

    }
}
